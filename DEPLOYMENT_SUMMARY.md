# LayuiAd<PERSON> Flask Docker 部署总结

## 完成的配置更改

### 1. 端口修改 ✅
- **原端口**: 83
- **新端口**: 8667
- **修改文件**: `app.py` (第74行)

### 2. Docker配置文件 ✅

#### 新增文件:
- `Dockerfile` - 基于Python 3.12.5的容器配置
- `docker-compose.yml` - Docker Compose服务配置
- `.dockerignore` - Docker构建忽略文件

#### 配置特点:
- Python版本: 3.12.5-slim
- 端口映射: 8667:8667
- 自动安装依赖
- 健康检查配置

### 3. 数据库挂载配置 ✅

#### DuckDB配置:
- **主机路径**: `../bills.duckdb` (项目根目录外)
- **容器路径**: `/data/bills.duckdb`
- **挂载方式**: 卷挂载，便于备份

#### SQLite配置:
- **主机路径**: `./users.db` (项目根目录内)
- **容器路径**: `/app/users.db`
- **挂载方式**: 卷挂载

### 4. 环境配置文件 ✅

#### 新增配置文件:
- `.env` - 环境变量配置
- `config.json` - 应用程序配置
- 数据库路径支持环境变量 `DUCKDB_PATH`

### 5. 启动脚本 ✅

#### 新增脚本:
- `docker-start.sh` - 自动化启动脚本
- `docker-stop.sh` - 停止服务脚本
- `setup-permissions.sh` - 权限设置脚本

#### 脚本功能:
- 自动检查Docker环境
- 创建必要目录
- 检查数据库文件
- 构建和启动容器
- 显示部署状态

### 6. 文档 ✅

#### 新增文档:
- `DOCKER_DEPLOYMENT.md` - 详细部署指南
- `DEPLOYMENT_SUMMARY.md` - 本总结文档

## 部署步骤

### 快速部署 (推荐)

```bash
# 1. 设置脚本权限
chmod +x setup-permissions.sh
./setup-permissions.sh

# 2. 启动服务
./docker-start.sh
```

### 手动部署

```bash
# 1. 构建并启动
docker-compose up --build -d

# 2. 检查状态
docker-compose ps

# 3. 查看日志
docker logs layuiadmin-flask-app
```

## 访问应用

- **本地访问**: http://localhost:8667
- **服务器访问**: http://服务器IP:8667

## 数据持久化

### 数据库文件位置:
- **DuckDB**: `../bills.duckdb` (容器外，便于备份)
- **SQLite**: `./users.db` (容器外，便于备份)

### 其他持久化目录:
- **日志**: `./logs/`
- **导出文件**: `./static/exports/`
- **用户摘要**: `./static/user_summary/`

## 备份建议

```bash
# 备份数据库
cp ../bills.duckdb ../bills.duckdb.backup.$(date +%Y%m%d_%H%M%S)
cp users.db users.db.backup.$(date +%Y%m%d_%H%M%S)

# 备份整个项目
tar -czf layuiadmin_backup_$(date +%Y%m%d_%H%M%S).tar.gz .
```

## 故障排除

### 常见问题:

1. **端口冲突**
   - 检查: `netstat -tlnp | grep 8667`
   - 解决: 修改docker-compose.yml中的端口映射

2. **权限问题**
   - 确保数据库文件有读写权限
   - 运行: `chmod 666 ../bills.duckdb users.db`

3. **容器启动失败**
   - 查看日志: `docker logs layuiadmin-flask-app`
   - 检查配置: `docker-compose config`

## 性能优化

### 已配置的优化:
- DuckDB内存限制: 2GB
- 多线程支持: 4线程
- 连接池: 最大10个连接
- 对象缓存: 已启用

### 监控命令:
```bash
# 查看容器资源使用
docker stats layuiadmin-flask-app

# 查看应用日志
docker logs -f layuiadmin-flask-app
```

## 安全建议

1. 修改`.env`文件中的`SECRET_KEY`
2. 配置防火墙规则
3. 定期备份数据库
4. 监控容器资源使用
5. 定期更新Docker镜像

## 下一步

1. 测试应用功能
2. 配置生产环境密钥
3. 设置自动备份脚本
4. 配置监控和日志收集
5. 设置SSL证书（如需要）

---

**部署完成！** 🎉

现在您的LayuiAdmin Flask应用已经配置为使用Docker Compose部署，支持：
- Python 3.12.5
- 端口8667
- DuckDB外部挂载
- 自动化部署脚本
- 完整的文档支持
