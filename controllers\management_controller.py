from flask import Blueprint, request, jsonify, session
from models.auth_model import AuthModel
from functools import wraps

blueprint = Blueprint('management', __name__, url_prefix='/api/management')
auth_model = AuthModel()

# --- Decorator for admin access ---
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = session.get('user')
        if not user or user.get('role') != 'admin':
            return jsonify({"code": 403, "msg": "无权访问，需要管理员权限"}), 403
        return f(*args, **kwargs)
    return decorated_function

# --- User Management Routes ---

@blueprint.route('/users', methods=['GET'])
@admin_required
def get_users():
    users = auth_model.get_all_users()
    return jsonify({"code": 0, "msg": "", "count": len(users), "data": users})

@blueprint.route('/users', methods=['POST'])
@admin_required
def add_user():
    data = request.get_json()
    if not data or 'username' not in data or 'password' not in data or 'position' not in data:
        return jsonify({"code": 400, "msg": "缺少必要参数"}), 400
    
    success, message = auth_model.add_user(data)
    if success:
        return jsonify({"code": 0, "msg": message})
    else:
        return jsonify({"code": 500, "msg": message}), 500

@blueprint.route('/users/<string:username>', methods=['PUT'])
@admin_required
def update_user(username):
    data = request.get_json()
    if not data:
        return jsonify({"code": 400, "msg": "请求体为空"}), 400
        
    success, message = auth_model.update_user(username, data)
    if success:
        return jsonify({"code": 0, "msg": message})
    else:
        return jsonify({"code": 500, "msg": message}), 500

@blueprint.route('/users/<string:username>', methods=['DELETE'])
@admin_required
def delete_user(username):
    success, message = auth_model.delete_user(username)
    if success:
        return jsonify({"code": 0, "msg": message})
    else:
        return jsonify({"code": 500, "msg": message}), 500

@blueprint.route('/users/reset-password', methods=['POST'])
@admin_required
def reset_user_password():
    data = request.get_json()
    username = data.get('username')
    new_password = data.get('password')

    if not username or not new_password:
        return jsonify({"code": 400, "msg": "缺少用户名或新密码"}), 400

    success, message = auth_model.reset_password(username, new_password)
    if success:
        return jsonify({"code": 0, "msg": message})
    else:
        return jsonify({"code": 500, "msg": message}), 500 