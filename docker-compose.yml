version: '3.8'

services:
  layuiadmin-flask:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: layuiadmin-flask-app
    ports:
      - "8667:8667"
    volumes:
      # 挂载DuckDB数据库文件（在项目根目录外面的那层目录）
      - ../bills.duckdb:/data/bills.duckdb
      # 挂载项目内的SQLite用户数据库
      - ./users.db:/app/users.db
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载静态文件导出目录
      - ./static/exports:/app/static/exports
      - ./static/user_summary:/app/static/user_summary
    environment:
      - FLASK_APP=app.py
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
      # 数据库路径环境变量
      - DUCKDB_PATH=/data/bills.duckdb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8667/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - layuiadmin-network

networks:
  layuiadmin-network:
    driver: bridge

# 可选：如果需要数据持久化卷
volumes:
  app-logs:
    driver: local
  app-exports:
    driver: local
