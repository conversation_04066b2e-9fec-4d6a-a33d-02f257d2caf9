<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>用户管理</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/layui/css/layui.css') }}" media="all">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/adminui/dist/css/admin.css') }}" media="all">
</head>
<body>

<div class="layui-card layadmin-header">
  <div class="layui-breadcrumb" lay-filter="breadcrumb">
    <a lay-href="">主页</a>
    <a><cite>用户管理</cite></a>
  </div>
</div>

<div class="layui-fluid">
  <div class="layui-card">
    <div class="layui-card-body">
      <div style="padding-bottom: 10px;">
        <button class="layui-btn" id="addUserBtn">添加用户</button>
      </div>

      <table id="userManagementTable" lay-filter="userManagementTable"></table>
    </div>
  </div>
</div>

<!-- Table Toolbar Actions -->
{% raw %}
<script type="text/html" id="table-toolbar-actions">
  <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
  <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="reset_password">重置密码</a>
  {{# if(d.role !== 'admin'){ }}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
  {{# } }}
</script>
{% endraw %}

<!-- Add/Edit User Form Template -->
<script type="text/html" id="userFormTemplate">
  <form class="layui-form" lay-filter="userForm" style="padding: 20px;">
    <div class="layui-form-item">
      <label class="layui-form-label">用户名/账号</label>
      <div class="layui-input-block">
        <input type="text" name="username" placeholder="请输入邮箱格式的用户名" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">职务</label>
      <div class="layui-input-block">
        <input type="text" name="position" placeholder="请输入职务" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item" id="passwordField">
      <label class="layui-form-label">密码</label>
      <div class="layui-input-block">
        <input type="password" name="password" placeholder="请输入密码" autocomplete="off" class="layui-input">
      </div>
    </div>
    <div class="layui-form-item" lay-filter="roleFilter">
        <label class="layui-form-label">角色</label>
        <div class="layui-input-block">
          <input type="radio" name="role" value="user" title="普通用户" checked>
          <input type="radio" name="role" value="admin" title="管理员">
        </div>
    </div>
    <div class="layui-form-item" style="text-align: right;">
        <button class="layui-btn" lay-submit lay-filter="formSubmit">提交</button>
        <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
    </div>
  </form>
</script>


<script src="{{ url_for('static', filename='res/layui/layui.js') }}"></script>
<script>
layui.use(['table', 'form', 'layer'], function(){
  var table = layui.table;
  var form = layui.form;
  var layer = layui.layer;
  var $ = layui.jquery;

  // Render User Table
  table.render({
    elem: '#userManagementTable',
    url: '/api/management/users',
    cols: [[
      {field: 'id', title: 'ID', width: 80, sort: true},
      {field: 'username', title: '用户名/账号', minWidth: 200},
      {field: 'position', title: '职务', minWidth: 150},
      {field: 'role', title: '角色', width: 100, templet: function(d){
        return d.role === 'admin' ? '<span class="layui-badge layui-bg-blue">管理员</span>' : '<span class="layui-badge layui-bg-green">普通用户</span>';
      }},
      {title: '操作', width: 200, align:'left', fixed: 'right', toolbar: '#table-toolbar-actions'}
    ]],
    page: true
  });

  // Open Add User Dialog
  $('#addUserBtn').on('click', function(){
    openUserDialog();
  });

  // Table row actions
  table.on('tool(userManagementTable)', function(obj){
    var data = obj.data;
    if(obj.event === 'del'){
      layer.confirm('确定要删除用户 ' + data.username + ' 吗？', function(index){
        $.ajax({
          url: '/api/management/users/' + data.username,
          type: 'DELETE',
          success: function(res){
            layer.close(index);
            if(res.code === 0){
                layer.msg('删除成功');
                table.reload('userManagementTable');
            } else {
                layer.msg(res.msg || '删除失败');
            }
          },
          error: function(xhr){
              layer.msg(xhr.responseJSON.msg || '请求失败');
          }
        });
      });
    } else if(obj.event === 'edit'){
      openUserDialog(data);
    } else if(obj.event === 'reset_password'){
      layer.prompt({
        formType: 1,
        title: '为 ' + data.username + ' 重置密码'
      }, function(value, index){
        $.ajax({
            url: '/api/management/users/reset-password',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({username: data.username, password: value}),
            success: function(res){
                layer.close(index);
                if(res.code === 0){
                    layer.msg('密码重置成功');
                } else {
                    layer.msg(res.msg || '操作失败');
                }
            },
            error: function(xhr){
                layer.msg(xhr.responseJSON.msg || '请求失败');
            }
        });
      });
    }
  });

  // Function to open Add/Edit Dialog
  function openUserDialog(data){
    var isEdit = !!data;
    var title = isEdit ? '编辑用户' : '添加用户';
    var userData = data || {};

    layer.open({
      type: 1,
      title: title,
      area: ['500px', '420px'],
      content: $('#userFormTemplate').html(),
      success: function(layero, index){
        // Hide password for edit
        if(isEdit){
            layero.find('#passwordField').hide();
            layero.find('input[name="username"]').attr('readonly', true).addClass('layui-disabled');
            // Cannot edit admin's role
            if(userData.role === 'admin'){
                layero.find('[lay-filter="roleFilter"]').hide();
            }
        }
        
        form.val('userForm', {
          "username": userData.username,
          "position": userData.position,
          "role": userData.role || 'user'
        });
        form.render();

        // Form submission
        form.on('submit(formSubmit)', function(formData){
            var apiData = formData.field;
            var url = isEdit ? '/api/management/users/' + userData.username : '/api/management/users';
            var type = isEdit ? 'PUT' : 'POST';

            // For edit, password is not sent
            if(isEdit){
                delete apiData.password;
            }

            $.ajax({
                url: url,
                type: type,
                contentType: 'application/json',
                data: JSON.stringify(apiData),
                success: function(res){
                    if(res.code === 0){
                        layer.close(index);
                        layer.msg(isEdit ? '更新成功' : '添加成功');
                        table.reload('userManagementTable');
                    } else {
                        layer.msg(res.msg || '操作失败');
                    }
                },
                error: function(xhr){
                    layer.msg(xhr.responseJSON.msg || '请求失败');
                }
            });

            return false;
        });

        // Cancel button
        layero.find('#cancelBtn').on('click', function(){
            layer.close(index);
        });
      }
    });
  }
});
</script>

</body>
</html> 