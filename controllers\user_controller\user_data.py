#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户数据控制器
处理用户基本数据相关的请求
"""

import json
import traceback
import pandas as pd
from flask import jsonify, request
from .base import UserControllerBase

class UserDataController(UserControllerBase):
    """用户数据控制器"""
    
    def __init__(self):
        """初始化用户数据控制器"""
        super().__init__()
    
    def get_user_data(self):
        """获取用户数据API - 适配LayUI表格格式"""
        # 获取分页参数
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=10, type=int)
        
        # 获取筛选参数
        username = request.args.get('username', '')
        date_range = request.args.get('date-range', '')
        
        print(f"接收到请求: page={page}, limit={limit}, username={username}, date_range={date_range}")
        
        # 解析日期范围
        start_date = ""
        end_date = ""
        if date_range:
            try:
                # 分割日期范围
                date_parts = date_range.split(' - ')
                if len(date_parts) == 2:
                    start_date, end_date = date_parts
                    print(f"解析日期范围: start_date={start_date}, end_date={end_date}")
            except Exception as e:
                print(f"日期解析错误: {e}")
        
        # 获取用户汇总数据
        try:
            print("开始查询数据...")
            if start_date and end_date:
                print(f"按日期范围查询: {start_date} - {end_date}")
                df = self.user_model.get_user_summary_by_date_range(username, start_date, end_date)
            else:
                print(f"按默认条件查询: username={username}, end_date={end_date}")
                df = self.user_model.get_user_summary(username, end_date)
                
            print(f"查询结果: {df.shape if df is not None else 'None'}")
            
            # 检查数据库中是否有交易明细表
            db_manager = self.db_manager
            with db_manager.get_connection() as conn:
                if conn:
                    tables = conn.execute("SHOW TABLES").fetchall()
                    print(f"数据库中的表: {[table[0] for table in tables]}")
                    
                    # 检查交易明细表中的记录数
                    if any(table[0] == 'transaction_details' for table in tables):
                        count = conn.execute("SELECT COUNT(*) FROM transaction_details").fetchone()[0]
                        print(f"交易明细表中的记录数: {count}")
                    else:
                        print("交易明细表不存在")
                else:
                    print("无法获取数据库连接")
                    
        except Exception as e:
            print(f"获取用户数据出错: {e}")
            traceback.print_exc()
            df = None
        
        # 准备响应数据
        if df is None or df.empty:
            print("没有数据获取")
            # 返回空数据
            return jsonify({
                "code": 0,
                "msg": "",
                "count": 0,
                "data": []
            })
        
        # 将DataFrame转换为JSON友好的格式
        data = []
        for idx, row in df.iterrows():
            # 创建基本项
            item = {
                "username": row.get("用户名", ""),
                "classify": row.get("客户名称", ""),
                "wealth": float(row.get("总支出金额", 0)),
                "income": float(row.get("总收入金额", 0)),
                "net_income": float(row.get("净收支", 0)),
                "transactions": int(row.get("交易总次数", 0)),
                "outgoing_transactions": int(row.get("支出次数", 0)),
                "incoming_transactions": int(row.get("收入次数", 0)),
                "current_balance": float(row.get("当前现金余额", 0)),
                "credit_limit": float(row.get("当前信用额度", 0))
            }
            
            # 处理特殊字段
            # OBS容量
            if "OBS容量" in row:
                item["obs_capacity"] = row["OBS容量"]
                # 添加数值用于排序
                if "OBS容量数值" in row:
                    item["obs_capacity_value"] = float(row["OBS容量数值"])
            else:
                # 计算默认OBS容量
                default_obs = float(row.get("总支出金额", 0)) / 1000000
                item["obs_capacity_value"] = default_obs
                if default_obs >= 1024:
                    item["obs_capacity"] = f"{default_obs/1024:.2f}TB"
                else:
                    item["obs_capacity"] = f"{default_obs:.2f}GB"
                
            # 累计作业数
            if "累计作业数" in row:
                item["experience"] = int(row["累计作业数"])
            else:
                item["experience"] = 0
                
            # 卡时数据
            if "共享卡时" in row:
                item["card_time"] = float(row["共享卡时"])
            else:
                item["card_time"] = 0
                
            if "专属卡时" in row:
                item["exclusive_card_time"] = float(row["专属卡时"])
            else:
                item["exclusive_card_time"] = 0
                
            if "专属卡时(除误差)" in row:
                item["exclusive_card_time_adjusted"] = float(row["专属卡时(除误差)"])
            else:
                item["exclusive_card_time_adjusted"] = 0
                
            # 处理日期时间格式
            if "最早交易时间" in row:
                earliest = row["最早交易时间"]
                if isinstance(earliest, pd.Timestamp):
                    item["earliest_transaction"] = earliest.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    item["earliest_transaction"] = str(earliest)
            else:
                item["earliest_transaction"] = ""
                
            if "最近交易时间" in row:
                latest = row["最近交易时间"]
                if isinstance(latest, pd.Timestamp):
                    item["latest_transaction"] = latest.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    item["latest_transaction"] = str(latest)
            else:
                item["latest_transaction"] = ""
            
            data.append(item)
        
        print(f"处理后的数据条数: {len(data)}")
        
        # 分页处理
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        page_data = data[start_idx:end_idx] if start_idx < len(data) else []
        
        # 返回LayUI表格格式的数据
        return jsonify({
            "code": 0,
            "msg": "",
            "count": len(data),
            "data": page_data
        })
        
    def get_connection_pool_info(self):
        """获取数据库连接池信息"""
        try:
            pool_info = self.db_manager.get_connection_info()
            
            return jsonify({
                "code": 0,
                "msg": "获取连接池信息成功",
                "data": pool_info
            })
        except Exception as e:
            print(f"获取连接池信息出错: {e}")
            traceback.print_exc()
            return jsonify({
                "code": 500,
                "msg": f"获取连接池信息出错: {str(e)}",
                "data": None
            })
            
    def get_default_date_range(self):
        """获取默认日期范围"""
        try:
            default_date_range = self.user_model.get_default_date_range()
            return jsonify({
                "code": 0,
                "msg": "获取默认日期范围成功",
                "data": default_date_range
            })
        except Exception as e:
            print(f"获取默认日期范围出错: {e}")
            traceback.print_exc()
            return jsonify({
                "code": 500,
                "msg": f"获取默认日期范围出错: {str(e)}",
                "data": None
            }) 