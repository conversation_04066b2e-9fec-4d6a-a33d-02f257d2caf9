#!/bin/bash

# ==========================================================
# SPCSP 应用停止脚本
# ==========================================================

PID_FILE="layuiAdmin_flask.pid"

echo "正在尝试停止应用..."

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "错误：未找到PID文件 ($PID_FILE)。"
    echo "应用可能未在运行，或PID文件已被删除。"
    exit 1
fi

# 读取PID
PID=$(cat "$PID_FILE")

# 检查PID是否存在且有效
if [ -z "$PID" ]; then
    echo "错误：PID文件为空。"
    rm -f "$PID_FILE"
    exit 1
fi

if ps -p $PID > /dev/null; then
    # 尝试优雅地停止进程
    echo "正在停止进程ID: $PID..."
    kill $PID
    
    # 等待进程退出
    sleep 2
    
    # 检查进程是否仍在运行
    if ps -p $PID > /dev/null; then
        echo "进程 $PID 未能优雅地停止，正在强制停止 (kill -9)..."
        kill -9 $PID
        sleep 1
    fi

    # 再次确认
    if ps -p $PID > /dev/null; then
        echo "错误：无法停止进程 $PID。"
    else
        echo "应用已成功停止。"
    fi
else
    echo "信息：根据PID $PID，找不到正在运行的应用进程。"
    echo "这可能是因为它已经被手动停止了。"
fi

# 清理PID文件
echo "正在清理PID文件..."
rm -f "$PID_FILE"

echo "停止操作完成。" 