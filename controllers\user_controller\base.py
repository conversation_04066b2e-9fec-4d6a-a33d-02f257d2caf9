#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户控制器基础类
包含通用方法和工具函数
"""

import os
import sys
import traceback
from datetime import datetime
import json

# 添加项目根目录到模块搜索路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from models.user_model import UserModel
from models.database import DatabaseManager

class UserControllerBase:
    """用户控制器基础类"""
    
    def __init__(self):
        """初始化基础控制器"""
        self.user_model = UserModel()
        self.db_manager = DatabaseManager.get_instance()
        
    def _parse_date_range(self, date_range):
        """解析日期范围字符串为开始日期和结束日期
        
        Args:
            date_range: 日期范围字符串，格式为 "YYYY-MM-DD - YYYY-MM-DD"
            
        Returns:
            tuple: (start_date, end_date) 或者 ("", "") 如果解析失败
        """
        start_date = ""
        end_date = ""
        if date_range:
            try:
                date_parts = date_range.split(' - ')
                if len(date_parts) == 2:
                    start_date, end_date = date_parts
            except Exception as e:
                print(f"解析日期范围出错: {e}")
        return start_date, end_date
    
    def _get_month_date_range(self, month):
        """获取指定月份的开始日期和结束日期
        
        Args:
            month: 月份字符串，格式为 "YYYY-MM"
            
        Returns:
            tuple: (start_date, end_date) 或者 ("", "") 如果解析失败
        """
        try:
            year, month_num = month.split('-')
            year = int(year)
            month_num = int(month_num)
            
            # 获取月份的开始日期
            start_date = f"{year}-{month_num:02d}-01 00:00:00"
            
            # 获取月末日期 (下个月的第一天减去1秒)
            if month_num == 12:
                end_date = f"{year + 1}-01-01 00:00:00"
            else:
                end_date = f"{year}-{month_num + 1:02d}-01 00:00:00"
            
            # 调整结束日期为当月最后一天的23:59:59
            import datetime
            end_datetime = datetime.datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S") - datetime.timedelta(seconds=1)
            end_date = end_datetime.strftime("%Y-%m-%d %H:%M:%S")
            
            return start_date, end_date
        except Exception as e:
            print(f"获取月份日期范围出错: {e}")
            return "", ""
    
    def _export_data_to_file(self, df, file_name, export_format, export_dir):
        """将数据导出到文件
        
        Args:
            df: 要导出的DataFrame
            file_name: 文件名
            export_format: 导出格式，'xlsx'或'csv'
            export_dir: 导出目录
            
        Returns:
            tuple: (success, message, file_path)
        """
        try:
            if df is None or df.empty:
                return False, "没有数据可导出", None
                
            file_path = os.path.join(export_dir, file_name)
            
            # 导出数据
            if export_format == 'xlsx':
                df.to_excel(file_path, index=False, engine='openpyxl')
            elif export_format == 'csv':
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            else:
                return False, f"不支持的导出格式: {export_format}", None
                
            return True, "导出成功", file_path
        except Exception as e:
            print(f"导出数据到文件出错: {e}")
            traceback.print_exc()
            return False, f"导出数据出错: {str(e)}", None 