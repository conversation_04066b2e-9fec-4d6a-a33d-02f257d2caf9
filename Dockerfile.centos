# 备用Dockerfile - 基于CentOS/EulerOS构建Python环境
FROM centos:7

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV FLASK_APP=app.py
ENV FLASK_ENV=production
ENV PATH="/usr/local/python3/bin:$PATH"

# 安装系统依赖和Python 3.12.5
RUN yum update -y && \
    yum groupinstall -y "Development Tools" && \
    yum install -y \
        wget \
        zlib-devel \
        bzip2-devel \
        openssl-devel \
        ncurses-devel \
        sqlite-devel \
        readline-devel \
        tk-devel \
        libffi-devel \
        xz-devel && \
    # 下载并编译Python 3.12.5
    cd /tmp && \
    wget https://mirrors.huaweicloud.com/python/3.12.5/Python-3.12.5.tgz && \
    tar -xzf Python-3.12.5.tgz && \
    cd Python-3.12.5 && \
    ./configure --prefix=/usr/local/python3 --enable-optimizations && \
    make -j$(nproc) && \
    make altinstall && \
    # 创建软链接
    ln -sf /usr/local/python3/bin/python3.12 /usr/local/python3/bin/python && \
    ln -sf /usr/local/python3/bin/python3.12 /usr/local/python3/bin/python3 && \
    ln -sf /usr/local/python3/bin/pip3.12 /usr/local/python3/bin/pip && \
    # 清理临时文件
    rm -rf /tmp/Python-3.12.5* && \
    yum clean all

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn && \
    pip install --upgrade pip

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p logs static/exports static/user_summary

# 设置权限
RUN chmod +x start_app.sh 2>/dev/null || true

# 暴露端口8667
EXPOSE 8667

# 启动应用
CMD ["python", "app.py"]
