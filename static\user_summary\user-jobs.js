/**
 * 用户作业详情模块
 */
// 确保layui已加载
if (typeof layui !== 'undefined') {
  // 用户作业模块定义
  window.UserJobs = {
    // 当前用户名
    currentUsername: '',
    
    // 初始化作业详情弹窗
    openJobsDialog: function(userData) {
      var that = this;
      var $ = layui.$;
      var layer = layui.layer;
      var table = layui.table;
      var form = layui.form;
      var laydate = layui.laydate;
      
      // 保存当前用户名
      this.currentUsername = userData.username;
      
      // 加载用户作业详情片段
      $.get('/api/user/jobs-fragment', function(content) {
        // 创建弹窗
        var jobsIndex = layer.open({
          type: 1,
          title: '用户作业详情 - ' + userData.username,
          area: ['1400px', '850px'],
          maxmin: true, // 添加最大化/最小化按钮
          shade: 0.3, // 增加遮罩
          btn: null, // 移除底部按钮
          content: content,
          success: function(layero, index) {
            // 初始化用户基本信息
            that.initUserBasicInfo(userData, layero);
            
            // 初始化日期选择器
            that.initDatePicker(layero);
            
            // 初始化表单事件
            that.initFormEvents(layero, index);
            
            // 初始化作业表格
            that.initJobsTable(layero, userData.username);
            
            // 初始化导出按钮
            that.initExportButton(layero);
          }
        });
      });
    },
    
    // 初始化用户基本信息
    initUserBasicInfo: function(userData, layero) {
      var $ = layui.$;
      
      // 填充用户基本信息
      layero.find('#detail-username').text(userData.username);
      layero.find('#detail-customer').text(userData.classify);
      
      // 初始作业总数和运行时长在API请求后更新
      layero.find('#detail-job-count').text('加载中...');
      layero.find('#detail-total-duration').text('加载中...');
    },
    
    // 初始化日期选择器
    initDatePicker: function(layero) {
      var laydate = layui.laydate;
      
      laydate.render({
        elem: layero.find('#jobs-date-range')[0],
        range: true,
        type: 'datetime',
        shortcuts: [
          {
            text: "上个月",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth();
              return [
                new Date(year, month - 1, 1, 0, 0, 0),
                new Date(year, month, 0, 23, 59, 59)
              ];
            }
          },
          {
            text: "本月",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth();
              return [
                new Date(year, month, 1, 0, 0, 0),
                new Date(year, month + 1, 0, 23, 59, 59)
              ];
            }
          },
          {
            text: "本年",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              return [
                new Date(year, 0, 1, 0, 0, 0),
                new Date(year, 11, 31, 23, 59, 59)
              ];
            }
          },
          {
            text: "去年",
            value: function(){
              var date = new Date();
              var year = date.getFullYear() - 1;
              return [
                new Date(year, 0, 1, 0, 0, 0),
                new Date(year, 11, 31, 23, 59, 59)
              ];
            }
          },
          {
            text: "最近一周",
            value: function(){
              var date = new Date();
              var lastWeek = new Date(date.getTime() - 7 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastWeek.getFullYear(), lastWeek.getMonth(), lastWeek.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近一月",
            value: function(){
              var date = new Date();
              var lastMonth = new Date(date.getTime() - 30 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastMonth.getFullYear(), lastMonth.getMonth(), lastMonth.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近三月",
            value: function(){
              var date = new Date();
              var lastThreeMonths = new Date(date.getTime() - 90 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastThreeMonths.getFullYear(), lastThreeMonths.getMonth(), lastThreeMonths.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近半年",
            value: function(){
              var date = new Date();
              var lastSixMonths = new Date(date.getFullYear(), date.getMonth() - 6, date.getDate());
              return [
                new Date(lastSixMonths.getFullYear(), lastSixMonths.getMonth(), lastSixMonths.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          }
        ]
      });
    },
    
    // 初始化表单事件
    initFormEvents: function(layero, index) {
      var that = this;
      var $ = layui.$;
      var form = layui.form;
      
      // 应用过滤按钮事件
      form.on('submit(jobs-filter-submit)', function(data){
        // 重新加载表格
        that.loadJobsData(layero);
        return false;
      });
      
      // 重置按钮事件
      layero.find('#reset-filter-btn').on('click', function(){
        layero.find('#jobs-filter-form')[0].reset();
        form.render(); // 重新渲染表单
        that.loadJobsData(layero);
      });
    },
    
    // 初始化作业表格
    initJobsTable: function(layero, username) {
      var that = this;
      var $ = layui.$;
      var table = layui.table;
      
      // 渲染表格
      table.render({
        elem: layero.find('#jobs-table'),
        url: '/api/user/jobs',
        where: {
          username: username
        },
        cols: [[
          {type: 'numbers', title: '序号', width: 60, align: 'center'},
          {field: '作业名称', title: '作业名称', minWidth: 250},
          {field: '客户名称', title: '客户名称', width: 180},
          {field: '总运行时长', title: '总运行时长', width: 150, sort: true},
          {field: '作业开始时间', title: '开始时间', width: 170, sort: true},
          {field: '作业结束时间', title: '结束时间', width: 170, sort: true}
        ]],
        page: {
          layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
          groups: 5
        },
        limit: 10,
        limits: [10, 20, 50, 100],
        skin: 'line row', // 添加行列边框风格
        even: true, // 开启隔行背景
        text: {
          none: '暂无作业记录'
        },
        parseData: function(res){
          // 更新总数和运行时长
          if(res.code === 0) {
            layero.find('#detail-job-count').text(res.count || 0);
            layero.find('#detail-total-duration').text(res.total_duration || '0秒');
          }
          
          return {
            "code": res.code,
            "msg": res.msg,
            "count": res.count,
            "data": res.data
          };
        }
      });
    },
    
    // 加载作业数据
    loadJobsData: function(layero) {
      var $ = layui.$;
      var table = layui.table;
      
      // 获取过滤条件
      var filterData = {
        username: this.currentUsername,
        'date-range': layero.find('#jobs-date-range').val()
      };
      
      // 重新加载表格
      table.reload('jobs-table', {
        where: filterData,
        page: {
          curr: 1 // 重置到第一页
        }
      });
    },
    
    // 初始化导出按钮
    initExportButton: function(layero) {
      var that = this;
      var $ = layui.$;
      var layer = layui.layer;
      
      layero.find('#export-jobs-btn').on('click', function(){
        var filterData = {
          username: that.currentUsername,
          'date-range': layero.find('#jobs-date-range').val()
        };
        
        // 显示导出进度
        var loadingIndex = layer.msg('正在导出数据，请稍候...', {
          icon: 16,
          time: 0,
          shade: 0.3
        });
        
        // 调用导出API
        $.ajax({
          url: '/api/export/user-jobs',
          type: 'GET',
          data: filterData,
          dataType: 'json',
          success: function(res) {
            layer.close(loadingIndex);
            
            if (res.code === 0) {
              // 导出成功，提示下载
              layer.msg('数据导出成功，共 ' + res.data.rows + ' 条记录', {icon: 1});
              
              // 创建下载链接并自动点击
              var link = document.createElement('a');
              link.href = res.data.url;
              link.download = res.data.file_name;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            } else {
              // 导出失败
              layer.msg(res.msg || '导出失败', {icon: 2});
            }
          },
          error: function(xhr, status, error) {
            layer.close(loadingIndex);
            layer.msg('导出请求失败: ' + error, {icon: 2});
          }
        });
      });
    }
  };
} else {
  console.error('Layui is required for UserJobs module');
} 