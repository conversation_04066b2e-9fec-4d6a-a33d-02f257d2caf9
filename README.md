# LayuiAdmin Flask版

这是一个使用Flask后端和LayuiAdmin前端框架的示例项目。

## 项目说明

本项目将LayuiAdmin框架与Flask后端集成，实现了component/table/resetPage.html的自定义分页表格功能。项目保持了原有LayuiAdmin的界面风格，同时使用Flask提供后端API支持。

## 安装

1. 安装依赖：
```
pip install -r requirements.txt
```

2. 运行应用：

Windows:
```
start_app.bat
```

Linux/Mac:
```
./start_app.sh
```

或者直接运行：
```
python app.py
```

3. 访问应用：
在浏览器中打开 http://127.0.0.1:83

## 功能

- 使用Flask作为后端
- 集成LayuiAdmin框架
- 实现了自定义分页表格示例
- 提供RESTful API接口
- 使用SQLite数据库进行用户认证
- 用户管理功能（仅管理员）

## 数据库

项目使用SQLite数据库进行用户认证和管理。初次运行时，系统会自动从`users.json`导入用户数据到SQLite数据库。

默认管理员账号：
- 用户名：<EMAIL>
- 密码：damon

## 项目结构

```
layuiAdmin_flask/
  ├── app.py                  # Flask应用主文件
  ├── requirements.txt        # 项目依赖
  ├── users.db                # SQLite数据库文件（自动创建）
  ├── users.json              # 初始用户数据（导入后可删除）
  ├── controllers/            # 控制器目录
  │   ├── auth_controller.py  # 认证控制器
  │   └── ...                 # 其他控制器
  ├── models/                 # 模型目录
  │   ├── auth_model.py       # 认证模型
  │   └── ...                 # 其他模型
  ├── static/                 # 静态资源目录
  │   └── res/                # LayuiAdmin资源
  │       ├── adminui/        # 主题核心代码
  │       ├── layui/          # 基础UI组件库
  │       ├── json/           # 模拟数据
  │       ├── config.js       # 配置文件
  │       └── index.js        # 入口模块
  └── templates/              # 模板目录
      ├── index.html          # 首页
      ├── login.html          # 登录页
      └── ...                 # 其他模板
```
