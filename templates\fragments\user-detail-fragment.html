<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>用户费用详情</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/layui/css/layui.css') }}" media="all">
  <style>
    .detail-form-item {
      margin-bottom: 8px;
    }
    .detail-form-label {
      display: inline-block;
      width: 120px;
      text-align: right;
      padding-right: 10px;
      font-weight: bold;
      white-space: nowrap; /* 防止标签文字折行 */
    }
    .detail-form-value {
      display: inline-block;
      min-width: 180px;
    }
    .money-positive {
      color: #009688;
    }
    .money-negative {
      color: #FF5722;
    }
    .money-different {
      color: #1E9FFF; /* 蓝色 */
    }
    .filter-form {
      margin-bottom: 15px;
      background-color: #f8f8f8;
      padding: 15px;
      border-radius: 4px;
    }
    .pagination {
      margin-top: 15px;
      text-align: center;
    }
    /* 悬浮高亮：修改鼠标hover行背景色 */
    .layui-table tbody tr:hover,
    .layui-table-hover,
    .layui-table[lay-skin="row"] tbody tr:hover {
      background-color: #f2f9ff !important; /* 浅蓝色，符合现代UI设计趋势 */
    }
  </style>
</head>
<body>
  <div class="layui-container" style="width: 100%; max-width: 1400px;">
    <!-- 用户详情 -->
    <div class="layui-card">
      <!-- <div class="layui-card-header">用户费用详情</div> -->
      <div class="layui-card-body">
        <div class="layui-row">
          <div class="layui-col-md6">
            <div class="detail-form-item">
              <span class="detail-form-label">用户名:</span>
              <span class="detail-form-value" id="detail-username"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">客户名称:</span>
              <span class="detail-form-value" id="detail-customer"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">账户ID:</span>
              <span class="detail-form-value" id="detail-account-id"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">交易总次数:</span>
              <span class="detail-form-value" id="detail-transaction-count"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">共享卡时:</span>
              <span class="detail-form-value" id="detail-shared-hours"></span>
            </div>
          </div>
          <div class="layui-col-md6">
            <div class="detail-form-item">
              <span class="detail-form-label">总支出金额:</span>
              <span class="detail-form-value" id="detail-total-expense"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">总收入金额:</span>
              <span class="detail-form-value" id="detail-total-income"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">净收支:</span>
              <span class="detail-form-value" id="detail-balance"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">专属卡时:</span>
              <span class="detail-form-value" id="detail-exclusive-hours"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">专属卡时(无误差):</span>
              <span class="detail-form-value" id="detail-exclusive-hours-adjusted"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">OBS容量:</span>
              <span class="detail-form-value" id="detail-obs-capacity"></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 过滤条件 -->
    <div class="layui-card">
      <!-- <div class="layui-card-header">交易记录</div> -->
      <div class="layui-card-body">
        <form class="layui-form filter-form" id="transaction-filter-form">
          <div class="layui-form-item" style="display: flex; flex-wrap: nowrap; align-items: center;">
            <div class="layui-inline" style="margin-right: 5px; margin-bottom: 0;">
              <label class="layui-form-label" style="width: auto; padding-right: 5px;">交易时间:</label>
              <div class="layui-input-inline" style="width: 320px;">
                <input type="text" name="date-range" id="transaction-date-range" placeholder=" - " autocomplete="off" class="layui-input">
              </div>
            </div>
            <div class="layui-inline" style="margin-right: 5px; margin-bottom: 0;">
              <label class="layui-form-label" style="width: auto; padding-right: 5px;">收支类型:</label>
              <div class="layui-input-inline" style="width: 100px;">
                <select name="income-type" id="income-type-select">
                  <option value="">全部</option>
                  <option value="支出">支出</option>
                  <option value="收入">收入</option>
                </select>
              </div>
            </div>
            <div class="layui-inline" style="margin-right: 5px; margin-bottom: 0;">
              <label class="layui-form-label" style="width: auto; padding-right: 5px;">交易类型:</label>
              <div class="layui-input-inline" style="width: 120px;">
                <select name="transaction-type" id="transaction-type-select">
                  <option value="">全部</option>
                  <option value="共享">共享</option>
                  <option value="专属">专属</option>
                  <option value="对象存储">对象存储</option>
                </select>
              </div>
            </div>
            <div class="layui-inline" style="margin-bottom: 0;">
              <button type="button" class="layui-btn" lay-submit lay-filter="transaction-filter-submit">应用过滤</button>
              <button type="button" class="layui-btn layui-btn-primary" id="reset-filter-btn">重置</button>
              <button type="button" class="layui-btn layui-btn-normal" id="export-transactions-btn"><i class="layui-icon layui-icon-export"></i> 导出数据</button>
            </div>
          </div>
        </form>
        
        <!-- 交易记录表格 -->
        <table id="transaction-table" lay-filter="transaction-table"></table>
        
        <!-- 分页区域 -->
        <div id="transaction-table-page" class="pagination"></div>
      </div>
    </div>
  </div>
</body>
</html> 