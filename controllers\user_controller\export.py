#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户数据导出控制器
处理用户数据导出相关的请求
"""

import os
import uuid
import traceback
from datetime import datetime
from flask import jsonify, request
from .base import UserControllerBase

class ExportController(UserControllerBase):
    """用户数据导出控制器"""
    
    def __init__(self):
        """初始化导出控制器"""
        super().__init__()
        
    def export_user_summary(self):
        """导出用户汇总数据"""
        try:
            # 获取请求参数
            username = request.args.get('username', '')
            date_range = request.args.get('date-range', '')
            export_format = request.args.get('format', 'xlsx')  # 默认为Excel格式
            export_mode = request.args.get('mode', 'current')  # 导出模式: current, single, multi
            
            # 创建导出目录（如果不存在）
            export_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'static', 'exports')
            os.makedirs(export_dir, exist_ok=True)
            
            # 根据导出模式执行不同的导出逻辑
            if export_mode == 'current':
                # 导出当前汇总列表
                # 解析日期范围
                start_date, end_date = self._parse_date_range(date_range)
                
                # 获取用户汇总数据
                if start_date and end_date:
                    df = self.user_model.get_user_summary_by_date_range(username, start_date, end_date)
                else:
                    df = self.user_model.get_user_summary(username)
                    
                if df is None or df.empty:
                    return jsonify({
                        "code": 1,
                        "msg": "没有数据可导出",
                        "data": None
                    })
                
                # 生成文件名
                date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                file_name = f"用户汇总_{date_str}_{uuid.uuid4().hex[:8]}.{export_format}"
                
                # 导出数据
                success, message, file_path = self._export_data_to_file(df, file_name, export_format, export_dir)
                
                if not success:
                    return jsonify({
                        "code": 1,
                        "msg": message,
                        "data": None
                    })
                
                # 返回文件下载URL
                download_url = f"/static/exports/{file_name}"
                return jsonify({
                    "code": 0,
                    "msg": "导出成功",
                    "data": {
                        "url": download_url,
                        "file_name": file_name,
                        "rows": len(df)
                    }
                })
            
            elif export_mode == 'single':
                # 单月导出
                month = request.args.get('month', '')  # 格式: YYYY-MM
                
                if not month:
                    return jsonify({
                        "code": 1,
                        "msg": "请指定要导出的月份",
                        "data": None
                    })
                
                # 获取月份的日期范围
                start_date, end_date = self._get_month_date_range(month)
                
                if not start_date or not end_date:
                    return jsonify({
                        "code": 1,
                        "msg": "月份格式不正确",
                        "data": None
                    })
                
                # 查询数据
                df = self.user_model.get_user_summary_by_date_range(username, start_date, end_date)
                
                if df is None or df.empty:
                    return jsonify({
                        "code": 1,
                        "msg": f"没有找到 {month} 月份的数据",
                        "data": None
                    })
                
                # 生成文件名
                file_name = f"用户汇总_{month}_{uuid.uuid4().hex[:8]}.{export_format}"
                
                # 导出数据
                success, message, file_path = self._export_data_to_file(df, file_name, export_format, export_dir)
                
                if not success:
                    return jsonify({
                        "code": 1,
                        "msg": message,
                        "data": None
                    })
                
                # 返回文件下载URL
                download_url = f"/static/exports/{file_name}"
                return jsonify({
                    "code": 0,
                    "msg": f"成功导出 {month} 月份数据",
                    "data": {
                        "url": download_url,
                        "file_name": file_name,
                        "rows": len(df)
                    }
                })
            
            elif export_mode == 'multi':
                # 多月导出
                start_month = request.args.get('start_month', '')  # 格式: YYYY-MM
                end_month = request.args.get('end_month', '')  # 格式: YYYY-MM
                
                if not start_month or not end_month:
                    return jsonify({
                        "code": 1,
                        "msg": "请指定开始和结束月份",
                        "data": None
                    })
                
                try:
                    # 解析开始月份
                    start_year, start_month_num = start_month.split('-')
                    start_year = int(start_year)
                    start_month_num = int(start_month_num)
                    
                    # 解析结束月份
                    end_year, end_month_num = end_month.split('-')
                    end_year = int(end_year)
                    end_month_num = int(end_month_num)
                    
                    # 计算总月份数
                    total_months = (end_year - start_year) * 12 + end_month_num - start_month_num + 1
                    
                    # 如果月份数为0或负数，说明有问题
                    if total_months <= 0:
                        return jsonify({
                            "code": 1,
                            "msg": "结束月份必须大于或等于开始月份",
                            "data": None
                        })
                    
                    # 创建ZIP文件
                    import zipfile
                    zip_filename = f"用户汇总_{start_month}_到_{end_month}_{uuid.uuid4().hex[:8]}.zip"
                    zip_path = os.path.join(export_dir, zip_filename)
                    
                    result_files = []
                    
                    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        # 从开始月份遍历到结束月份
                        current_year = start_year
                        current_month = start_month_num
                        
                        for i in range(total_months):
                            # 获取当前处理的月份字符串
                            month_str = f"{current_year}-{current_month:02d}"
                            
                            # 获取月份的日期范围
                            month_start_date, month_end_date = self._get_month_date_range(month_str)
                            
                            # 查询数据
                            df = self.user_model.get_user_summary_by_date_range(username, month_start_date, month_end_date)
                            
                            if df is not None and not df.empty:
                                # 临时文件名
                                temp_file = f"用户汇总_{month_str}.{export_format}"
                                temp_path = os.path.join(export_dir, temp_file)
                                
                                # 导出数据到临时文件
                                success, _, _ = self._export_data_to_file(df, temp_file, export_format, export_dir)
                                
                                if success:
                                    # 添加到ZIP文件
                                    zipf.write(temp_path, os.path.basename(temp_path))
                                    
                                    # 删除临时文件
                                    os.remove(temp_path)
                                    
                                    # 记录结果
                                    result_files.append({
                                        'month': month_str,
                                        'rows': len(df)
                                    })
                            
                            # 移动到下一个月
                            if current_month == 12:
                                current_year += 1
                                current_month = 1
                            else:
                                current_month += 1
                    
                    # 如果没有数据，返回错误
                    if not result_files:
                        # 删除空的ZIP文件
                        if os.path.exists(zip_path):
                            os.remove(zip_path)
                            
                        return jsonify({
                            "code": 1,
                            "msg": f"没有找到 {start_month} 到 {end_month} 期间的数据",
                            "data": None
                        })
                    
                    # 返回ZIP文件下载URL
                    download_url = f"/static/exports/{zip_filename}"
                    return jsonify({
                        "code": 0,
                        "msg": f"成功导出 {start_month} 到 {end_month} 期间的数据，共 {len(result_files)} 个月",
                        "data": {
                            "url": download_url,
                            "file_name": zip_filename,
                            "months": len(result_files),
                            "details": result_files
                        }
                    })
                    
                except Exception as e:
                    print(f"多月导出出错: {e}")
                    traceback.print_exc()
                    return jsonify({
                        "code": 500,
                        "msg": f"多月导出出错: {str(e)}",
                        "data": None
                    })
            
            else:
                return jsonify({
                    "code": 1,
                    "msg": f"不支持的导出模式: {export_mode}",
                    "data": None
                })
            
        except Exception as e:
            print(f"导出用户汇总数据出错: {e}")
            traceback.print_exc()
            return jsonify({
                "code": 500,
                "msg": f"导出用户汇总数据出错: {str(e)}",
                "data": None
            })
            
    def export_user_transactions(self):
        """导出用户交易记录"""
        try:
            # 获取请求参数
            username = request.args.get('username', '')
            date_range = request.args.get('date-range', '')
            income_type = request.args.get('income-type', '')
            transaction_type = request.args.get('transaction-type', '')
            export_format = request.args.get('format', 'xlsx')  # 默认为Excel格式
            
            if not username:
                return jsonify({
                    "code": 1,
                    "msg": "用户名不能为空",
                    "data": None
                })
            
            # 构建过滤条件
            filters = [f"用户名 = '{username}'"]
            
            # 解析日期范围
            start_date, end_date = self._parse_date_range(date_range)
            
            if start_date:
                filters.append(f"交易时间 >= '{start_date}'")
            if end_date:
                filters.append(f"交易时间 <= '{end_date}'")
            
            # 收支类型过滤
            if income_type:
                filters.append(f"收支类型 = '{income_type}'")
            
            # 交易类型过滤（对应备注字段）
            if transaction_type:
                filters.append(f"备注 LIKE '%{transaction_type}%'")
            
            # 获取所有用户交易记录（不分页）
            transactions_data = self.user_model.get_user_transactions(username, filters, page=1, page_size=1000000)
            
            if not transactions_data or 'data' not in transactions_data or transactions_data['data'] is None or transactions_data['data'].empty:
                return jsonify({
                    "code": 1,
                    "msg": "没有交易记录可导出",
                    "data": None
                })
            
            # 获取数据
            df = transactions_data['data']
            
            # 创建导出目录（如果不存在）
            export_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'static', 'exports')
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成文件名
            date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_name = f"{username}_交易记录_{date_str}_{uuid.uuid4().hex[:8]}.{export_format}"
            
            # 导出数据
            success, message, file_path = self._export_data_to_file(df, file_name, export_format, export_dir)
            
            if not success:
                return jsonify({
                    "code": 1,
                    "msg": message,
                    "data": None
                })
            
            # 返回文件下载URL
            download_url = f"/static/exports/{file_name}"
            return jsonify({
                "code": 0,
                "msg": "导出成功",
                "data": {
                    "url": download_url,
                    "file_name": file_name,
                    "rows": len(df)
                }
            })
            
        except Exception as e:
            print(f"导出用户交易记录出错: {e}")
            traceback.print_exc()
            return jsonify({
                "code": 500,
                "msg": f"导出用户交易记录出错: {str(e)}",
                "data": None
            })
            
    def export_user_jobs(self):
        """导出用户作业记录"""
        try:
            # 获取请求参数
            username = request.args.get('username', '')
            date_range = request.args.get('date-range', '')
            export_format = request.args.get('format', 'xlsx')  # 默认为Excel格式
            
            if not username:
                return jsonify({
                    "code": 1,
                    "msg": "用户名不能为空",
                    "data": None
                })
            
            # 解析日期范围
            start_date, end_date = self._parse_date_range(date_range)
            
            # 获取用户作业记录
            jobs_data = self.user_model.get_user_jobs(username, "", start_date, end_date)
            
            if jobs_data is None or jobs_data.empty:
                return jsonify({
                    "code": 1,
                    "msg": "没有作业记录可导出",
                    "data": None
                })
            
            # 删除"运行时长(秒)"列，只用于后端计算
            if "运行时长(秒)" in jobs_data.columns:
                jobs_data = jobs_data.drop(columns=["运行时长(秒)"])
            
            # 创建导出目录（如果不存在）
            export_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'static', 'exports')
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成文件名
            date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_name = f"{username}_作业记录_{date_str}_{uuid.uuid4().hex[:8]}.{export_format}"
            
            # 导出数据
            success, message, file_path = self._export_data_to_file(jobs_data, file_name, export_format, export_dir)
            
            if not success:
                return jsonify({
                    "code": 1,
                    "msg": message,
                    "data": None
                })
            
            # 返回文件下载URL
            download_url = f"/static/exports/{file_name}"
            return jsonify({
                "code": 0,
                "msg": "导出成功",
                "data": {
                    "url": download_url,
                    "file_name": file_name,
                    "rows": len(jobs_data)
                }
            })
            
        except Exception as e:
            print(f"导出用户作业记录出错: {e}")
            traceback.print_exc()
            return jsonify({
                "code": 500,
                "msg": f"导出用户作业记录出错: {str(e)}",
                "data": None
            })
