#!/bin/bash

# Docker国内镜像源配置脚本

echo "=== Docker 国内镜像源配置脚本 ==="
echo ""

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本，或使用sudo"
    exit 1
fi

# 备份现有配置
DOCKER_CONFIG_DIR="/etc/docker"
DOCKER_DAEMON_CONFIG="$DOCKER_CONFIG_DIR/daemon.json"

if [ -f "$DOCKER_DAEMON_CONFIG" ]; then
    echo "备份现有Docker配置..."
    cp "$DOCKER_DAEMON_CONFIG" "$DOCKER_DAEMON_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 创建Docker配置目录
mkdir -p "$DOCKER_CONFIG_DIR"

# 写入镜像源配置
echo "配置Docker国内镜像源..."
cat > "$DOCKER_DAEMON_CONFIG" << 'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://docker.mirrors.ustc.edu.cn"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false,
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF

echo "Docker镜像源配置完成"
echo ""

# 重启Docker服务
echo "重启Docker服务..."
systemctl restart docker

# 检查Docker状态
if systemctl is-active --quiet docker; then
    echo "✅ Docker服务重启成功"
else
    echo "❌ Docker服务重启失败，请检查配置"
    exit 1
fi

# 验证配置
echo ""
echo "验证Docker镜像源配置..."
docker info | grep -A 10 "Registry Mirrors" || echo "配置验证完成"

echo ""
echo "=== 配置完成 ==="
echo "现在可以正常拉取Docker镜像了"
echo ""
echo "测试命令："
echo "docker pull registry.cn-hangzhou.aliyuncs.com/library/python:3.12.5-slim"
