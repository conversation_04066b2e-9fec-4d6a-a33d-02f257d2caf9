<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>控制台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/layui/css/layui.css') }}" media="all">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/adminui/dist/css/admin.css') }}" media="all">
  <style>
    :root {
      --primary-color: #4A90E2; /* A nice blue */
      --success-color: #50E3C2; /* A teal/mint green */
      --warning-color: #F5A623; /* An orange */
      --purple-color: #9d5ce5;
      --text-color: #4A4A4A;
      --text-color-secondary: #9B9B9B;
      --bg-color: #f7f9fc;
      --card-bg-color: #ffffff;
      --border-color: #e8e8e8;
    }

    body {
        background-color: var(--bg-color) !important;
    }
    .layui-fluid {
      padding: 20px;
    }
    .layui-card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        border: none;
        background-color: var(--card-bg-color);
    }

    .layui-card-header {
        font-weight: 600;
        font-size: 16px;
        color: var(--text-color);
        border-bottom: 1px solid var(--border-color);
    }
    
    /* New Stat Card Style */
    .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background-color: var(--card-bg-color);
        transition: all 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    }
    .stat-card .stat-icon {
        font-size: 32px;
        width: 64px;
        height: 64px;
        border-radius: 50%;
        margin-right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    .stat-card .stat-info h4 {
        font-size: 14px;
        color: var(--text-color-secondary);
        margin-bottom: 8px;
        font-weight: 500;
    }
    .stat-card .stat-info p {
        font-size: 28px;
        font-weight: 600;
        color: var(--text-color);
    }

    /* Updated Stat Card Icon Style for better alignment and new look */
    .icon-blue { background-color: rgba(74, 144, 226, 0.2); color: var(--primary-color); }
    .icon-green { background-color: rgba(80, 227, 194, 0.25); color: #28a745; } /* Adjusted green for better contrast */
    .icon-orange { background-color: rgba(245, 166, 35, 0.2); color: var(--warning-color); }
    .icon-purple { background-color: rgba(157, 92, 229, 0.2); color: var(--purple-color); }

    /* System Status card styles */
    .system-status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 14px 0;
        border-bottom: 1px solid var(--border-color);
    }
    .system-status-item:last-child {
        border-bottom: none;
    }
    .system-status-item .status-label {
        display: flex;
        align-items: center;
        color: var(--text-color);
    }
    .system-status-item .status-label .layui-icon {
        margin-right: 10px;
        font-size: 20px;
    }
    .system-status-item .status-value {
        font-weight: 600;
        color: var(--text-color);
    }
    
    .exclusive-count {
      font-weight: bold;
      color: #FF5722;
    }
    
    /* Class to make cards in a row equal height */
    .layui-row.equal-height-row {
        display: flex;
        flex-wrap: wrap;
    }
    .equal-height-row > [class*="layui-col-"] {
        display: flex;
        flex-direction: column;
    }
    .equal-height-row > [class*="layui-col-"] > .layui-card {
        flex-grow: 1;
    }
    
    /* 悬浮高亮：修改鼠标hover行背景色 */
    .layui-table tbody tr:hover,
    .layui-table-hover,
    .layui-table[lay-skin="row"] tbody tr:hover {
      background-color: #f2f9ff !important;
    }
  </style>
</head>
<body>
  <div class="layui-fluid">
    <!-- Top row for key metrics -->
    <div class="layui-row layui-col-space15">
        <!-- Total Users -->
        <div class="layui-col-sm6 layui-col-md3">
            <div class="layui-card stat-card">
                <div class="stat-icon icon-blue">
                    <i class="layui-icon layui-icon-user"></i>
                </div>
                <div class="stat-info">
                    <h4>总用户数</h4>
                    <p id="totalUsers">--</p>
                </div>
            </div>
        </div>
        <!-- Total Jobs -->
        <div class="layui-col-sm6 layui-col-md3">
            <div class="layui-card stat-card">
                <div class="stat-icon icon-purple">
                    <i class="layui-icon layui-icon-app"></i>
                </div>
                <div class="stat-info">
                    <h4>总作业数</h4>
                    <p id="totalJobs">--</p>
                </div>
            </div>
        </div>
        <!-- Total OBS -->
        <div class="layui-col-sm6 layui-col-md3">
            <div class="layui-card stat-card">
                <div class="stat-icon icon-green">
                    <i class="layui-icon layui-icon-template-1"></i>
                </div>
                <div class="stat-info">
                    <h4>总OBS容量 (TB)</h4>
                    <p id="totalObs">--</p>
                </div>
            </div>
        </div>
        <!-- DB Size -->
        <div class="layui-col-sm6 layui-col-md3">
            <div class="layui-card stat-card">
                <div class="stat-icon icon-orange">
                    <i class="layui-icon layui-icon-auz"></i>
                </div>
                <div class="stat-info">
                    <h4>数据库大小</h4>
                    <p id="db-size">--</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Second Row: Main Content -->
    <div class="layui-row layui-col-space15 equal-height-row" style="margin-top: 15px;">
        <!-- Exclusive Pools -->
        <div class="layui-col-md7">
            <div class="layui-card">
                <div class="layui-card-header">
                    专属资源池
                    <span style="float: right; font-size: 12px; color: #999; font-weight: normal;">近2个月</span>
                </div>
                <div class="layui-card-body">
                    <table id="exclusive-users-table" lay-filter="exclusive-users-table"></table>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="layui-col-md5">
            <div class="layui-card">
                <div class="layui-card-header">系统状态</div>
                <div class="layui-card-body">
                    <div class="system-status-item">
                        <span class="status-label"><i class="layui-icon layui-icon-table" style="color: #4A90E2;"></i> 收支明细记录</span>
                        <span class="status-value" id="trans-count">--</span>
                    </div>
                    <div class="system-status-item">
                        <span class="status-label"><i class="layui-icon layui-icon-file-b" style="color: #50E3C2;"></i> 账单信息记录</span>
                        <span class="status-value" id="bill-count">--</span>
                    </div>
                    <div class="system-status-item">
                        <span class="status-label"><i class="layui-icon layui-icon-link" style="color: #F5A623;"></i> 数据库连接</span>
                        <span class="status-value" id="conn-count">--</span>
                    </div>
                    <div class="system-status-item">
                        <span class="status-label"><i class="layui-icon layui-icon-set-sm" style="color: #9B9B9B;"></i> 运行模式</span>
                        <span class="status-value" id="read-only-status">--</span>
                    </div>
                     <div class="system-status-item">
                        <span class="status-label"><i class="layui-icon layui-icon-file" style="color: #9B9B9B;"></i> 数据库路径</span>
                        <span class="status-value" id="db-path" style="font-size: 12px; font-weight: normal; color: #999; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px;" title="">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
  
  <script src="{{ url_for('static', filename='res/layui/layui.js') }}"></script>
  <script>
  layui.config({
    base: '{{ url_for("static", filename="res/") }}' // 静态资源所在路径
  }).extend({
    index: 'index'
  }).use(['index', 'console', 'table', 'element'], function(){
    var $ = layui.$
    ,admin = layui.admin
    ,element = layui.element
    ,table = layui.table;
    
    // 加载统计数据
    function loadDashboardStats() {
      $.ajax({
        url: '/api/dashboard/stats',
        type: 'GET',
        dataType: 'json',
        success: function(res) {
          if (res.code === 0) {
            // 更新统计数字
            $('#totalUsers').text(formatNumber(res.data.total_users || 0));
            $('#totalJobs').text(formatNumber(res.data.total_jobs || 0));
            $('#totalObs').text(formatNumber(res.data.total_obs || 0));
            
            // 渲染专属资源池用户表格
            renderExclusiveUsersTable(res.data.exclusive_users || []);
          } else {
            layer.msg('获取统计数据失败: ' + res.msg);
          }
        },
        error: function(xhr, status, error) {
          layer.msg('获取统计数据请求失败: ' + error);
        }
      });
    }
    
    // 渲染专属资源池用户表格
    function renderExclusiveUsersTable(data) {
      table.render({
        elem: '#exclusive-users-table'
        ,data: data
        ,cols: [[
          {type: 'numbers', title: '序号', width: 60, align: 'center'}
          ,{field: 'username', title: '用户名', width: 120, sort: true}
          ,{field: 'customer', title: '客户名称', minWidth: 200, sort: true}
          ,{field: 'count', title: '开通个数', width: 120, sort: true, align: 'center', templet: function(d){
            return '<span class="exclusive-count">' + d.count + '</span>';
          }}
        ]]
        ,page: {
          layout: ['prev', 'page', 'next', 'count', 'limit']
        }
        ,limit: 5
        ,limits: [5, 10, 20]
        ,skin: 'line row' // 添加行列边框风格
        ,even: true // 开启隔行背景
        ,height: 360 // 固定高度，以适应右侧卡片
        ,text: {
          none: '暂无专属资源池用户'
        }
      });
    }
    
    // 格式化数字，添加千分位分隔符
    function formatNumber(num) {
      if (typeof num !== 'number') return num;
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // 加载数据库状态信息
    function loadDBStatus() {
      $.ajax({
        url: '/api/db/status',
        type: 'GET',
        success: function(res) {
          if(res.code === 0) {
            var data = res.data;
            $('#db-size').text(data.db_file_size);
            $('#db-path').text(data.db_file).attr('title', data.db_file);
            $('#trans-count').text(data.total_records.transaction_details ? formatNumber(data.total_records.transaction_details) : '0');
            $('#bill-count').text(data.total_records.bill_information ? formatNumber(data.total_records.bill_information) : '0');
            $('#conn-count').text(data.connection_pool.current_connections + ' / ' + data.connection_pool.max_connections);
            
            // 更新运行模式为徽章样式
            $('#read-only-status').html(data.connection_pool.read_only 
              ? '<span class="layui-badge layui-bg-green">只读模式</span>' 
              : '<span class="layui-badge layui-bg-green">读写模式</span>'
            );
          } else {
            layer.msg('获取数据库状态失败: ' + res.msg);
          }
        },
        error: function(xhr) {
          layer.msg('请求出错: ' + xhr.statusText);
        }
      });
    }
    
    // 加载页面时执行
    loadDashboardStats();
    loadDBStatus();
    
    // 定时刷新状态
    setInterval(loadDBStatus, 30000);  // 30秒刷新一次
  });
  </script>
</body>
</html> 