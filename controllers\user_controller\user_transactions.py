#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户交易控制器
处理用户交易记录相关的请求
"""

import traceback
import pandas as pd
from flask import jsonify, request
from .base import UserControllerBase

class UserTransactionsController(UserControllerBase):
    """用户交易控制器"""
    
    def __init__(self):
        """初始化用户交易控制器"""
        super().__init__()
    
    def get_user_transactions(self):
        """获取用户交易记录API - 默认每页10条，支持10/20/50/100条每页"""
        try:
            # 获取请求参数
            username = request.args.get('username', '')
            date_range = request.args.get('date-range', '')
            income_type = request.args.get('income-type', '')
            transaction_type = request.args.get('transaction-type', '')
            page = request.args.get('page', default=1, type=int)
            limit = request.args.get('limit', default=10, type=int)
            
            print(f"接收到交易记录请求: username={username}, date_range={date_range}, income_type={income_type}, transaction_type={transaction_type}, page={page}, limit={limit}")
            
            if not username:
                print("用户名不能为空")
                return jsonify({
                    "code": 1,
                    "msg": "用户名不能为空",
                    "count": 0,
                    "data": []
                })
            
            # 构建过滤条件
            filters = [f"用户名 = '{username}'"]
            
            # 解析日期范围
            start_date = ""
            end_date = ""
            if date_range:
                try:
                    date_parts = date_range.split(' - ')
                    if len(date_parts) == 2:
                        start_date, end_date = date_parts
                        filters.append(f"交易时间 >= '{start_date}'")
                        filters.append(f"交易时间 <= '{end_date}'")
                except Exception as e:
                    print(f"解析日期范围出错: {e}")
            
            # 收支类型过滤
            if income_type:
                filters.append(f"收支类型 = '{income_type}'")
            
            # 交易类型过滤（对应备注字段）
            if transaction_type:
                filters.append(f"备注 LIKE '%{transaction_type}%'")
            
            print(f"构建的过滤条件: {filters}")
            
            # 获取用户交易记录
            # 使用本地UserModel类
            transactions_data = self.user_model.get_user_transactions(username, filters, page, limit)
            
            # 处理结果
            if transactions_data and 'data' in transactions_data and transactions_data['data'] is not None:
                # 转换DataFrame为JSON友好格式
                transactions_list = []
                for idx, row in transactions_data['data'].iterrows():
                    item = {}
                    for col in transactions_data['data'].columns:
                        value = row[col]
                        if pd.isna(value):
                            item[col] = ""
                        elif isinstance(value, pd.Timestamp):
                            item[col] = value.strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            item[col] = str(value)
                    transactions_list.append(item)
                
                print(f"成功获取交易记录，总记录数: {transactions_data['total_count']}, 当前页记录数: {len(transactions_list)}")
                return jsonify({
                    "code": 0,
                    "msg": "",
                    "count": transactions_data['total_count'],
                    "total_pages": transactions_data['total_pages'],
                    "data": transactions_list
                })
            else:
                print("没有找到匹配的交易记录")
                return jsonify({
                    "code": 0,
                    "msg": "没有找到交易记录",
                    "count": 0,
                    "total_pages": 1,
                    "data": []
                })
                
        except Exception as e:
            print(f"获取用户交易记录出错: {e}")
            traceback.print_exc()
            return jsonify({
                "code": 500,
                "msg": f"获取用户交易记录出错: {str(e)}",
                "data": None
            }) 