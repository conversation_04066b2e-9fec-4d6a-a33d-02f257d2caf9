#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
控制台统计控制器
处理控制台统计数据相关的请求
"""

import traceback
from flask import jsonify
from .base import UserControllerBase

class DashboardController(UserControllerBase):
    """控制台统计控制器"""
    
    def __init__(self):
        """初始化控制台统计控制器"""
        super().__init__()
    
    def get_dashboard_stats(self):
        """获取控制台首页所需的统计数据"""
        try:
            stats = {}
            
            # 1. 总用户数 - 从交易明细表获取不重复用户名
            with self.db_manager.get_connection() as conn:
                if conn:
                    # 总用户数
                    total_users = conn.execute("SELECT COUNT(DISTINCT \"用户名\") FROM transaction_details").fetchone()[0]
                    stats['total_users'] = total_users
                    
                    # 总作业数 - 从账单信息表查询共享作业
                    total_jobs = conn.execute("SELECT COUNT(DISTINCT \"名称/ID\") FROM bill_information WHERE \"名称/ID\" LIKE '%共享%'").fetchone()[0]
                    stats['total_jobs'] = total_jobs
                    
                    # 总OBS容量 - 使用提供的SQL查询
                    obs_sql = """
                    WITH 目标账期 AS (
                        SELECT 
                            MAX(b."账期") AS 目标账期 
                        FROM bill_information b
                        WHERE b."产品" LIKE '对象存储 OBS%'
                    ),
                    用户OBS数据 AS (
                        SELECT 
                            b."用户名",
                            b."名称/ID" AS 存储桶,
                            b."资源用量",
                            ROW_NUMBER() OVER (PARTITION BY b."用户名", b."名称/ID" ORDER BY b."支付时间" DESC, b."账单号" DESC) AS rn
                        FROM bill_information b
                        JOIN 目标账期 t ON b."账期" = t.目标账期
                        WHERE b."产品" LIKE '对象存储 OBS%'
                    )
                    SELECT 
                        SUM(CASE 
                            WHEN o."资源用量" LIKE '%GB%' THEN CAST(REPLACE(o."资源用量", 'GB', '') AS DOUBLE)
                            WHEN o."资源用量" LIKE '%TB%' THEN CAST(REPLACE(o."资源用量", 'TB', '') AS DOUBLE) * 1024
                            ELSE 0.0 
                        END) AS 总OBS容量GB
                    FROM 用户OBS数据 o
                    WHERE o.rn = 1
                    """
                    
                    total_obs_gb = conn.execute(obs_sql).fetchone()[0]
                    # 转换为TB并保留两位小数
                    total_obs_tb = total_obs_gb / 1024 if total_obs_gb else 0
                    stats['total_obs'] = round(total_obs_tb, 2)
                    
                    # 获取最近2个月专属资源池用户
                    exclusive_users_sql = """
                    WITH 最近账期 AS (
                        SELECT DISTINCT "账期"
                        FROM bill_information
                        ORDER BY "账期" DESC
                        LIMIT 2
                    )
                    SELECT 
                        b."用户名",
                        b."客户",
                        COUNT(*) AS 使用次数
                    FROM bill_information b
                    JOIN 最近账期 r ON b."账期" = r."账期"
                    WHERE b."产品" LIKE '%专属%'
                    GROUP BY b."用户名", b."客户"
                    ORDER BY 使用次数 DESC, b."用户名"
                    LIMIT 8
                    """
                    
                    results = conn.execute(exclusive_users_sql).fetchall()
                    exclusive_users = []
                    for row in results:
                        exclusive_users.append({
                            'username': row[0],
                            'customer': row[1],
                            'count': row[2]
                        })
                    stats['exclusive_users'] = exclusive_users
                    
            return jsonify({
                "code": 0,
                "msg": "获取控制台统计数据成功",
                "data": stats
            })
            
        except Exception as e:
            print(f"获取控制台统计数据出错: {e}")
            traceback.print_exc()
            
            return jsonify({
                "code": 1,
                "msg": f"获取控制台统计数据失败: {str(e)}",
                "data": {
                    "total_users": 0,
                    "total_jobs": 0,
                    "total_obs": 0,
                    "exclusive_users": []
                }
            }) 