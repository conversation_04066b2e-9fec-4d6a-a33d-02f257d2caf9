/**
 * 用户费用详情模块
 */
// 确保layui已加载
if (typeof layui !== 'undefined') {
  // 用户详情模块定义
  window.UserDetail = {
    // 当前用户名
    currentUsername: '',
    
    // 初始化费用详情弹窗
    openDetailDialog: function(userData) {
      var that = this;
      var $ = layui.$;
      var layer = layui.layer;
      var table = layui.table;
      var form = layui.form;
      var laydate = layui.laydate;
      
      // 保存当前用户名
      this.currentUsername = userData.username;
      
      // 加载用户详情片段
      $.get('/api/user/detail-fragment', function(content) {
        // 创建弹窗
        var detailIndex = layer.open({
          type: 1,
          title: '用户费用详情 - ' + userData.username,
          area: ['1400px', '850px'],
          maxmin: true, // 添加最大化/最小化按钮
          shade: 0.3, // 增加遮罩
          btn: null, // 移除底部按钮
          content: content,
          success: function(layero, index) {
            // 初始化用户详情数据
            that.initUserDetailData(userData, layero);
            
            // 初始化日期选择器
            that.initDatePicker(layero);
            
            // 初始化下拉菜单
            that.initFilterDropdowns(layero);
            
            // 初始化表单事件
            that.initFormEvents(layero, index);
            
            // 初始化表格
            that.initTransactionTable(layero, userData.username);
            
            // 初始化导出按钮
            that.initExportButton(layero);
          }
        });
      });
    },
    
    // 初始化用户详情数据
    initUserDetailData: function(userData, layero) {
      var $ = layui.$;
      
      // 填充用户基本信息
      layero.find('#detail-username').text(userData.username);
      layero.find('#detail-customer').text(userData.classify);
      layero.find('#detail-account-id').text(userData.username); // 使用用户名作为账户ID
      
      // 格式化并填充金额数据
      var formatMoney = function(value, addColor) {
        var formatted = (typeof value === 'number') ? value.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }) : value;
        
        if (addColor) {
          var cls = (value >= 0) ? 'money-positive' : 'money-negative';
          return '<span class="' + cls + '">' + formatted + '</span>';
        }
        return formatted;
      };
      
      layero.find('#detail-total-expense').html(formatMoney(userData.wealth));
      layero.find('#detail-total-income').html(formatMoney(userData.income));
      layero.find('#detail-balance').html(formatMoney(userData.net_income, true));
      layero.find('#detail-transaction-count').text(formatMoney(userData.transactions, false));
      
      // 卡时数据
      layero.find('#detail-shared-hours').text(formatMoney(userData.card_time, false));
      layero.find('#detail-exclusive-hours').text(formatMoney(userData.exclusive_card_time, false));
      
      // 处理专属卡时(除误差)，不同时显示为蓝色
      var exclusiveTimeAdjusted = userData.exclusive_card_time_adjusted || 0;
      var exclusiveTime = userData.exclusive_card_time || 0;
      var isDifferent = Math.abs(exclusiveTimeAdjusted - exclusiveTime) > 0.01;
      if (isDifferent) {
        layero.find('#detail-exclusive-hours-adjusted').html('<span class="money-different">' + formatMoney(exclusiveTimeAdjusted, false) + '</span>');
      } else {
        layero.find('#detail-exclusive-hours-adjusted').text(formatMoney(exclusiveTimeAdjusted, false));
      }
      
      // OBS容量
      layero.find('#detail-obs-capacity').text(userData.obs_capacity || '0.00GB');
    },
    
    // 初始化日期选择器
    initDatePicker: function(layero) {
      var laydate = layui.laydate;
      
      laydate.render({
        elem: layero.find('#transaction-date-range')[0],
        range: true,
        type: 'datetime',
        shortcuts: [
          {
            text: "上个月",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth();
              return [
                new Date(year, month - 1, 1, 0, 0, 0),
                new Date(year, month, 0, 23, 59, 59)
              ];
            }
          },
          {
            text: "本月",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              var month = date.getMonth();
              return [
                new Date(year, month, 1, 0, 0, 0),
                new Date(year, month + 1, 0, 23, 59, 59)
              ];
            }
          },
          {
            text: "本年",
            value: function(){
              var date = new Date();
              var year = date.getFullYear();
              return [
                new Date(year, 0, 1, 0, 0, 0),
                new Date(year, 11, 31, 23, 59, 59)
              ];
            }
          },
          {
            text: "去年",
            value: function(){
              var date = new Date();
              var year = date.getFullYear() - 1;
              return [
                new Date(year, 0, 1, 0, 0, 0),
                new Date(year, 11, 31, 23, 59, 59)
              ];
            }
          },
          {
            text: "最近一周",
            value: function(){
              var date = new Date();
              var lastWeek = new Date(date.getTime() - 7 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastWeek.getFullYear(), lastWeek.getMonth(), lastWeek.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近一月",
            value: function(){
              var date = new Date();
              var lastMonth = new Date(date.getTime() - 30 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastMonth.getFullYear(), lastMonth.getMonth(), lastMonth.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近三月",
            value: function(){
              var date = new Date();
              var lastThreeMonths = new Date(date.getTime() - 90 * 24 * 60 * 60 * 1000);
              return [
                new Date(lastThreeMonths.getFullYear(), lastThreeMonths.getMonth(), lastThreeMonths.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          },
          {
            text: "最近半年",
            value: function(){
              var date = new Date();
              var lastSixMonths = new Date(date.getFullYear(), date.getMonth() - 6, date.getDate());
              return [
                new Date(lastSixMonths.getFullYear(), lastSixMonths.getMonth(), lastSixMonths.getDate(), 0, 0, 0),
                new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)
              ];
            }
          }
        ]
      });
    },
    
    // 初始化下拉菜单
    initFilterDropdowns: function(layero) {
      var $ = layui.$;
      var form = layui.form;
      
      // 收支类型下拉菜单
      var incomeTypeSelect = layero.find('#income-type-select');
      incomeTypeSelect.empty(); // 清空已有选项
      incomeTypeSelect.append('<option value="">全部</option>');
      incomeTypeSelect.append('<option value="支出">支出</option>');
      incomeTypeSelect.append('<option value="收入">收入</option>');
      
      // 交易类型下拉菜单（对应备注字段）
      var transactionTypeSelect = layero.find('#transaction-type-select');
      transactionTypeSelect.empty(); // 清空已有选项
      transactionTypeSelect.append('<option value="">全部</option>');
      transactionTypeSelect.append('<option value="共享">共享</option>');
      transactionTypeSelect.append('<option value="专属">专属</option>');
      transactionTypeSelect.append('<option value="对象存储">对象存储</option>');
      
      // 重新渲染表单
      form.render('select');
    },
    
    // 初始化表单事件
    initFormEvents: function(layero, index) {
      var that = this;
      var $ = layui.$;
      var form = layui.form;
      
      // 应用过滤按钮事件
      form.on('submit(transaction-filter-submit)', function(data){
        // 重新加载表格
        that.loadTransactionData(layero);
        return false;
      });
      
      // 重置按钮事件
      layero.find('#reset-filter-btn').on('click', function(){
        layero.find('#transaction-filter-form')[0].reset();
        form.render(); // 重新渲染表单
        that.loadTransactionData(layero);
      });
    },
    
    // 初始化交易记录表格
    initTransactionTable: function(layero, username) {
      var that = this;
      var $ = layui.$;
      var table = layui.table;
      
      // 渲染表格
      table.render({
        elem: layero.find('#transaction-table'),
        url: '/api/user/transactions',
        where: {
          username: username
        },
        cols: [[
          {type: 'numbers', title: '序号', width: 60, align: 'center'},
          {field: '交易编号', title: '交易编号', width: 200, sort: true},
          {field: '交易时间', title: '交易时间', width: 170, sort: true},
          {field: '收支类型', title: '收支类型', width: 110, sort: true},
          {field: '交易类型', title: '交易类型', width: 110},
          {field: '交易渠道', title: '交易渠道', width: 110},
          {field: '金额', title: '金额(元)', width: 110, sort: true, align: 'right', templet: function(d){
            var value = parseFloat(d.金额 || 0);
            var cls = d.收支类型 === '支出' ? 'money-negative' : 'money-positive';
            var displayValue = value.toLocaleString('zh-CN', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
            return '<span class="'+ cls +'">'+ displayValue +'</span>';
          }},
          {field: '备注', title: '备注', minWidth: 150},
          {field: '账期', title: '账期', width: 110}
        ]],
        page: {
          layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
          groups: 5
        },
        limit: 10,
        limits: [10, 20, 50, 100],
        skin: 'line row', // 添加行列边框风格
        even: true, // 开启隔行背景
        text: {
          none: '暂无交易记录'
        },
        parseData: function(res){
          return {
            "code": res.code,
            "msg": res.msg,
            "count": res.count,
            "data": res.data
          };
        }
      });
    },
    
    // 加载交易记录数据
    loadTransactionData: function(layero) {
      var $ = layui.$;
      var table = layui.table;
      
      // 获取过滤条件
      var filterData = {
        username: this.currentUsername,
        'date-range': layero.find('#transaction-date-range').val(),
        'income-type': layero.find('#income-type-select').val(),
        'transaction-type': layero.find('#transaction-type-select').val()
      };
      
      // 重新加载表格
      table.reload('transaction-table', {
        where: filterData,
        page: {
          curr: 1 // 重置到第一页
        }
      });
    },
    
    // 初始化导出按钮
    initExportButton: function(layero) {
      var that = this;
      var $ = layui.$;
      var layer = layui.layer;
      
      layero.find('#export-transactions-btn').on('click', function(){
        var filterData = {
          username: that.currentUsername,
          'date-range': layero.find('#transaction-date-range').val(),
          'income-type': layero.find('#income-type-select').val(),
          'transaction-type': layero.find('#transaction-type-select').val()
        };
        
        // 显示导出进度
        var loadingIndex = layer.msg('正在导出数据，请稍候...', {
          icon: 16,
          time: 0,
          shade: 0.3
        });
        
        // 调用导出API
        $.ajax({
          url: '/api/export/user-transactions',
          type: 'GET',
          data: filterData,
          dataType: 'json',
          success: function(res) {
            layer.close(loadingIndex);
            
            if (res.code === 0) {
              // 导出成功，提示下载
              layer.msg('数据导出成功，共 ' + res.data.rows + ' 条记录', {icon: 1});
              
              // 创建下载链接并自动点击
              var link = document.createElement('a');
              link.href = res.data.url;
              link.download = res.data.file_name;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            } else {
              // 导出失败
              layer.msg(res.msg || '导出失败', {icon: 2});
            }
          },
          error: function(xhr, status, error) {
            layer.close(loadingIndex);
            layer.msg('导出请求失败: ' + error, {icon: 2});
          }
        });
      });
    }
  };
} else {
  console.error('Layui is required for UserDetail module');
} 