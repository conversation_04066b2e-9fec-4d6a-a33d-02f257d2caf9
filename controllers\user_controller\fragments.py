#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
界面片段控制器
处理界面片段渲染相关的请求
"""

from flask import render_template
from .base import UserControllerBase

class FragmentsController(UserControllerBase):
    """界面片段控制器"""
    
    def __init__(self):
        """初始化界面片段控制器"""
        super().__init__()
    
    def render_user_detail_fragment(self):
        """渲染用户费用详情片段"""
        return render_template('fragments/user-detail-fragment.html')
    
    def render_user_jobs_fragment(self):
        """渲染用户作业详情片段"""
        return render_template('fragments/user-jobs-fragment.html') 