#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
作业数据相关功能
"""

import pandas as pd
import traceback
from .base import DBConnection

class JobsMixin:
    """作业数据相关功能"""
    
    def add_cumulative_jobs_to_summary(self, summary_df, end_date="", start_date=""):
        """将累计作业数添加到用户汇总中"""
        try:
            print("添加累计作业数据...")
            # 检查bill_information表是否存在
            if not self._check_table_exists("bill_information"):
                        self.logger.warning("bill_information表不存在，无法获取累计作业数据")
                        summary_df['累计作业数'] = 0
                        return summary_df
            
            # 构建日期条件
            date_condition = ""
            if start_date and end_date:
                date_condition = f'AND b."统计开始时间" >= \'{start_date} 00:00:00\' AND b."统计开始时间" <= \'{end_date} 23:59:59\''
            elif start_date:
                date_condition = f'AND b."统计开始时间" >= \'{start_date} 00:00:00\''
            elif end_date:
                date_condition = f'AND b."统计开始时间" <= \'{end_date} 23:59:59\''
            
            # 获取累计作业数据，使用传入的日期过滤条件 - 使用与原始项目相同的SQL
            jobs_sql = f"""
            WITH 共享作业数据 AS (
                SELECT 
                    b."用户名",
                    b."名称/ID",
                    b."统计开始时间"
                FROM bill_information b
                WHERE b."产品" LIKE '%共享%'
                {date_condition}
            )
            SELECT 
                j."用户名",
                COUNT(DISTINCT j."名称/ID") AS 累计作业数
            FROM 共享作业数据 j
            GROUP BY j."用户名"
            """
            
            # 执行查询
            print(f"执行SQL: {jobs_sql}")
            jobs_data = self.db_manager.execute_query(jobs_sql)
            
            if jobs_data is not None and not jobs_data.empty:
                print(f"作业数据: {jobs_data.shape}")
                # 将累计作业数据合并到用户汇总中
                summary_df = summary_df.merge(jobs_data, on='用户名', how='left')
                # 填充NaN值为0
                summary_df['累计作业数'] = summary_df['累计作业数'].fillna(0).astype(int)
            else:
                print("没有获取到作业数据")
                # 如果没有获取到作业数据，添加一个全为0的列
                summary_df['累计作业数'] = 0
                
            return summary_df
            
        except Exception as e:
            self.logger.error(f"获取累计作业数据时出错: {str(e)}")
            traceback.print_exc()
            # 出错时添加一个全为0的列
            summary_df['累计作业数'] = 0
            return summary_df
            
    def get_user_jobs(self, username, customer="", start_date="", end_date=""):
        """获取用户作业详情
        
        Args:
            username: 用户名
            customer: 客户名称（不再使用）
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            DataFrame: 包含作业名称、运行时长、开始时间、结束时间的数据框
        """
        try:
            # 检查bill_information表是否存在
            with DBConnection(self.db_manager) as conn:
                tables = conn.execute("SHOW TABLES").fetchall()
                if not any(table[0] == 'bill_information' for table in tables):
                    print("警告: bill_information表不存在，无法获取用户作业数据")
                    return None
            
            # 构建日期条件
            date_condition = ""
            if start_date and end_date:
                date_condition = f'AND b."统计开始时间" >= \'{start_date} 00:00:00\' AND b."统计结束时间" <= \'{end_date} 23:59:59\''
            elif start_date:
                date_condition = f'AND b."统计开始时间" >= \'{start_date} 00:00:00\''
            elif end_date:
                date_condition = f'AND b."统计结束时间" <= \'{end_date} 23:59:59\''
            
            # 首先获取所有匹配记录
            all_jobs_sql = f"""
            SELECT 
                b."用户名",
                b."客户" AS 客户名称,
                b."名称/ID" AS 作业名称,
                b."资源用量" AS 运行时长,
                b."统计开始时间" AS 开始时间,
                b."统计结束时间" AS 结束时间
            FROM bill_information b
            WHERE b."产品" LIKE '%共享%'
            AND b."用户名" = ?
            {date_condition}
            ORDER BY b."统计开始时间" DESC
            """
            
            all_jobs = self.db_manager.execute_query(all_jobs_sql, [username])
            
            if all_jobs is None or all_jobs.empty:
                return None
            
            # 对运行时长进行处理并聚合
            # 1. 创建一个包含秒数的列
            all_jobs['运行时长(秒)'] = all_jobs['运行时长'].apply(self._convert_duration_to_seconds)
            
            # 2. 按作业名称分组聚合数据
            aggregated_data = []
            for job_name, group in all_jobs.groupby('作业名称'):
                # 计算总运行时长
                total_seconds = group['运行时长(秒)'].sum()
                formatted_duration = self._format_seconds_to_duration(total_seconds)
                
                # 获取最早开始时间和最晚结束时间
                earliest_start = group['开始时间'].min()
                latest_end = group['结束时间'].max()
                
                aggregated_data.append({
                    '用户名': username,
                    '客户名称': group['客户名称'].iloc[0],
                    '作业名称': job_name,
                    '总运行时长': formatted_duration,
                    '作业开始时间': earliest_start,
                    '作业结束时间': latest_end,
                    '运行时长(秒)': total_seconds  # 添加秒数用于后续计算
                })
            
            result_df = pd.DataFrame(aggregated_data)
            
            # 按运行时长(秒)降序排序
            result_df = result_df.sort_values('运行时长(秒)', ascending=False)
            
            return result_df
            
        except Exception as e:
            print(f"获取用户作业详情时出错: {str(e)}")
            traceback.print_exc()
            return None 