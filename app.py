from flask import (
    Flask,
    render_template,
    jsonify,
    request,
    send_from_directory,
    session,
    redirect,
    url_for,
)
import sys
import os

# 将当前目录添加到模块搜索路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入数据库和控制器
from models.database import DatabaseManager
from controllers.user_controller import user_controller
from controllers.db_controller import blueprint as db_controller_blueprint
from controllers.auth_controller import blueprint as auth_controller_blueprint
from controllers.management_controller import (
    blueprint as management_controller_blueprint,
)

app = Flask(__name__)
# WARNING: This secret key is for development only.
# In production, use a secure, randomly generated key and load it from a config file or environment variable.
app.secret_key = "dev-secret-key"

# 初始化数据库管理器
db_manager = DatabaseManager.get_instance()

# 注册蓝图
app.register_blueprint(user_controller.blueprint)
app.register_blueprint(db_controller_blueprint)
app.register_blueprint(auth_controller_blueprint)
app.register_blueprint(management_controller_blueprint)

# --- Main Routes ---


@app.route("/")
def index():
    if "user" not in session:
        return redirect(url_for("login"))
    return render_template("index.html", user=session.get("user"))


@app.route("/login")
def login():
    return render_template("login.html")


@app.route("/home/<USER>")
def console():
    if "user" not in session:
        return redirect(url_for("login"))
    return render_template("console.html")


@app.route("/user/management")
def user_management():
    if "user" not in session or session["user"].get("role") != "admin":
        return "无权访问", 403
    return render_template("user-management.html")


# --- Existing Routes ---


@app.route("/user/data-summary")
def user_data_summary():
    if "user" not in session:
        return redirect(url_for("login"))
    return render_template("user-data-summary.html")


@app.route("/views/system/theme")
def theme_view():
    return send_from_directory("static/res/views/system", "theme.html")


@app.route("/views/system/about")
def about_view():
    return send_from_directory("static/res/views/system", "about.html")


@app.route("/views/system/more")
def more_view():
    return send_from_directory("static/res/views/system", "more.html")


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8667, debug=True)
