<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>客户数据汇总</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/layui/css/layui.css') }}" media="all">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/adminui/dist/css/admin.css') }}" media="all">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/modules/common.css') }}" media="all">
</head>
<style>
  /* 将分页条放在右边 */
  .layui-table-page {
    text-align: right;
  }
  .layui-table-page > div {
    justify-content: flex-end;
    display: flex;
  }
  .money-positive {
    color: #009688;
  }
  .money-negative {
    color: #FF5722;
  }
  .money-different {
    color: #1E9FFF; /* 蓝色 */
  }
  /* 表头居中显示 */
  .layui-table-header th .layui-table-cell {
    text-align: center !important;
    justify-content: center !important;
  }
  /* 悬浮高亮：修改鼠标hover行背景色 */
  .layui-table tbody tr:hover,
  .layui-table-hover,
  .layui-table[lay-skin="row"] tbody tr:hover {
    background-color: #f2f9ff !important; /* 浅蓝色，符合现代UI设计趋势 */
  }
</style>
<body>

  <div class="layui-card layadmin-header">
    <div class="layui-breadcrumb" lay-filter="breadcrumb">
      <a lay-href="/">主页</a>
      <a><cite>客户信息</cite></a>
      <a><cite>客户数据汇总</cite></a>
    </div>
  </div>
  
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">客户查询</div>
          <div class="layui-card-body user-query-container">
            <div class="layui-form">
              <div class="layui-form-item">
                <div class="layui-inline" style="margin-right: 0;">
                  <label class="layui-form-label search-label">查询:</label>
                  <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" name="username" placeholder="输入用户名或客户名称进行查询..." autocomplete="off" class="layui-input">
                  </div>
                </div>
                <div class="layui-inline">
                  <label class="layui-form-label date-range-label">日期范围:</label>
                  <div class="layui-input-inline" style="width: 300px;">
                    <input type="text" id="date-range" name="date-range" placeholder=" - " autocomplete="off" class="layui-input">
                  </div>
                </div>
                <div class="layui-inline">
                  <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="LAY-user-front-search">
                    <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>搜索
                  </button>
                  <button class="layui-btn layuiadmin-btn-useradmin" id="btn-reset">
                    <i class="layui-icon layui-icon-refresh layuiadmin-button-btn"></i>重置
                  </button>
                </div>
              </div>
            </div>
            
            <div class="layui-inline" style="margin-bottom: 10px;">
              <button class="layui-btn layuiadmin-btn-useradmin" id="btn-export">
                <i class="layui-icon layui-icon-download-circle layuiadmin-button-btn"></i>导出数据
              </button>
              <button class="layui-btn layuiadmin-btn-useradmin" id="btn-refresh">
                <i class="layui-icon layui-icon-refresh-3 layuiadmin-button-btn"></i>刷新数据
              </button>
            </div>
            
            <table id="LAY-user-data-summary" lay-filter="LAY-user-data-summary"></table>
            
            <script type="text/html" id="table-operation">
              <a class="layui-btn layui-btn-xs" lay-event="check">
                <i class="layui-icon layui-icon-app"></i>作业
              </a>
              <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="usage">
                <i class="layui-icon layui-icon-rmb"></i>费用
              </a>
            </script>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 引入基础JS库 -->
  <script src="{{ url_for('static', filename='res/layui/layui.js') }}"></script>
  <!-- 引入JS模块文件 -->
  <script src="{{ url_for('static', filename='user_summary/user-detail.js') }}"></script>
  <script src="{{ url_for('static', filename='user_summary/user-jobs.js') }}"></script>
  <script src="{{ url_for('static', filename='user_summary/user-summary.js') }}"></script>
  
  <script>
  layui.config({
    base: '{{ url_for("static", filename="res/") }}' // 静态资源所在路径
  }).extend({
    index: 'index'
  }).use(['index', 'table', 'form', 'laydate'], function(){
    // 初始化用户数据汇总模块
    if (window.UserSummary) {
      window.UserSummary.init();
    } else {
      console.error('客户数据汇总模块加载失败');
    }
  });
  </script>
</body>
</html> 