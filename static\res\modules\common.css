/* 自定义样式 */
.user-query-container {
  padding-left: 10px;
}

.user-query-container .layui-form-label {
  padding-left: 0;
}

.user-query-container .layui-btn-group {
  margin-left: 10px;
}

.user-query-container .layui-table {
  margin-left: 10px;
  width: calc(100% - 20px);
}

/* 表格数字右对齐 */
.layui-table-cell-number {
  text-align: right !important;
}

/* 表格金额格式化 */
.money-positive {
  color: green;
}

.money-negative {
  color: red;
}

/* 改进表格样式 */
.layui-table-view {
  margin: 10px 0;
}

/* 改进按钮样式 */
.layui-btn-group {
  margin-bottom: 10px;
}

/* 改进搜索区域样式 */
.search-area {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.search-label {
  width: 60px;
  padding-left: 0;
  padding-right: 10px;
}

.date-range-label {
  width: 80px;
}

/* 响应式调整 */
@media screen and (max-width: 992px) {
  .layui-form-item .layui-inline {
    margin-bottom: 10px;
  }
} 