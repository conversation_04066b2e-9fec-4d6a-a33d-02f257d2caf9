#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库状态控制器
处理数据库状态查询功能
"""

import os
import json
import logging
from flask import Blueprint, request, jsonify
import traceback
import re

# 导入模型
from models.database import DatabaseManager
from models.db_model import DBModel

# 创建蓝图
blueprint = Blueprint('db', __name__, url_prefix='/api/db')

class DBController:
    def __init__(self):
        """初始化数据库状态控制器"""
        self.db_manager = DatabaseManager.get_instance()
        self.db_model = DBModel(self.db_manager)
        # 设置日志
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("db_controller")
        if not logger.handlers:
            # 确保logs目录存在
            logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
                
            handler = logging.FileHandler(os.path.join(logs_dir, "db_status.log"))
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
# 创建控制器实例
db_controller = DBController()

@blueprint.route('/status', methods=['GET'])
def get_db_status():
    """获取数据库状态信息"""
    try:
        # 获取数据库连接池信息
        pool_info = {
            'max_connections': db_controller.db_manager.max_connections,
            'current_connections': db_controller.db_manager.get_connection_count(),
            'read_only': db_controller.db_manager.read_only
        }
        
        # 获取数据库文件大小
        db_file_size = db_controller.db_model.get_database_file_size()
        
        # 确保文件大小以MB为单位显示
        if isinstance(db_file_size, str) and "MB" not in db_file_size and "GB" not in db_file_size:
            # 尝试提取数字部分并转换为MB
            size_match = re.search(r'([\d.]+)', db_file_size)
            if size_match:
                size_num = float(size_match.group(1))
                if "KB" in db_file_size:
                    db_file_size = f"{size_num/1024:.2f} MB"
                elif "B" in db_file_size and "KB" not in db_file_size and "MB" not in db_file_size:
                    db_file_size = f"{size_num/(1024*1024):.2f} MB"
        
        # 获取总记录数信息
        total_records = {}
        db_conn = db_controller.db_manager.get_connection()
        if db_conn:
            # 尝试获取主要表的记录数
            try:
                # 收支明细表
                result = db_conn.execute("SELECT COUNT(*) FROM transaction_details").fetchone()
                total_records['transaction_details'] = result[0] if result else 0
            except Exception as e:
                db_controller.logger.error(f"获取收支明细表记录数失败: {str(e)}")
                total_records['transaction_details'] = 0
                
            try:
                # 账单信息表
                result = db_conn.execute("SELECT COUNT(*) FROM bill_information").fetchone()
                total_records['bill_information'] = result[0] if result else 0
            except Exception as e:
                db_controller.logger.error(f"获取账单信息表记录数失败: {str(e)}")
                total_records['bill_information'] = 0
            
            # 释放连接
            db_controller.db_manager.release_connection()
        
        return jsonify({
            "code": 0,
            "msg": "",
            "data": {
                "db_file": db_controller.db_manager.db_path,
                "db_file_size": db_file_size,
                "connection_pool": pool_info,
                "total_records": total_records
            }
        })
    except Exception as e:
        error_msg = f"获取数据库状态失败: {str(e)}"
        db_controller.logger.error(error_msg)
        db_controller.logger.error(traceback.format_exc())
        return jsonify({"code": 500, "msg": error_msg}) 