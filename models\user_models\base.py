#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户模型基础类和工具函数
"""

import logging
import pandas as pd
import re
from functools import wraps
import time
import traceback

from ..database import DatabaseManager, DBConnection, DatabaseConnection

# 缓存装饰器
def query_cache(ttl_seconds=300):
    """带过期时间的缓存装饰器"""
    def decorator(func):
        cache = {}
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            key = str(args) + str(kwargs)
            
            # 检查缓存是否有效
            now = time.time()
            if key in cache and now - cache[key]['timestamp'] < ttl_seconds:
                return cache[key]['result']
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[key] = {
                'result': result,
                'timestamp': now
            }
            
            return result
        
        # 添加清除缓存的方法
        wrapper.clear_cache = lambda: cache.clear()
        
        return wrapper
    return decorator

class UserModelBase:
    """用户数据模型基类"""
    def __init__(self):
        """初始化用户模型"""
        try:
            print("初始化UserModel...")
            self.db_manager = DatabaseManager.get_instance()
            print(f"数据库路径: {self.db_manager.db_path}")
            
            # 设置日志
            self.logger = logging.getLogger('user_model')
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
            
            print("UserModel初始化完成")
        except Exception as e:
            print(f"UserModel初始化出错: {e}")
            traceback.print_exc()
    
    def _check_materialized_view_exists(self, view_name):
        """检查物化视图是否存在"""
        try:
            with DBConnection(self.db_manager) as conn:
                if conn:
                    tables = conn.execute("SHOW TABLES").fetchall()
                    return any(table[0] == view_name for table in tables)
                return False
        except Exception as e:
            self.logger.error(f"检查物化视图时出错: {e}")
            return False

    def _check_table_exists(self, table_name):
        """检查表是否存在"""
        try:
            with DBConnection(self.db_manager) as conn:
                if conn:
                    tables = conn.execute("SHOW TABLES").fetchall()
                    exists = any(table[0] == table_name for table in tables)
                    print(f"表 '{table_name}' 存在: {exists}")
                    return exists
                return False
        except Exception as e:
            self.logger.error(f"检查表时出错: {e}")
            return False
            
    def _convert_duration_to_seconds(self, duration_str):
        """将不同格式的运行时长转换为秒数
        
        支持的格式:
        - 43分47秒
        - 1小时0分0秒
        - 12秒
        """
        try:
            if not duration_str or not isinstance(duration_str, str):
                return 0
            
            total_seconds = 0
            
            # 处理小时
            hour_match = re.search(r'(\d+)小时', duration_str)
            if hour_match:
                total_seconds += int(hour_match.group(1)) * 3600
            
            # 处理分钟
            minute_match = re.search(r'(\d+)分', duration_str)
            if minute_match:
                total_seconds += int(minute_match.group(1)) * 60
            
            # 处理秒
            second_match = re.search(r'(\d+)秒', duration_str)
            if second_match:
                total_seconds += int(second_match.group(1))
            
            return total_seconds
        except Exception as e:
            print(f"转换运行时长出错: {str(e)}")
            return 0
        
    def _format_seconds_to_duration(self, seconds):
        """将秒数格式化为可读的时长格式"""
        try:
            if seconds == 0:
                return "0秒"
            
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            secs = seconds % 60
            
            if hours > 0:
                return f"{hours}小时{minutes}分{secs}秒"
            elif minutes > 0:
                return f"{minutes}分{secs}秒"
            else:
                return f"{secs}秒"
        except Exception as e:
            print(f"格式化时长出错: {str(e)}")
            return "0秒"
            
    def _reorder_summary_columns(self, df):
        """重新排列汇总结果的列顺序"""
        if df is None or df.empty:
            return df
            
        # 基本列顺序
        base_cols = ["用户名", "客户名称"]
        
        # 添加可选列
        optional_cols = []
        for col in ["OBS容量", "累计作业数", "共享卡时", "专属卡时"]:
            if col in df.columns:
                optional_cols.append(col)
                
        # 添加其他列
        other_cols = [col for col in df.columns if col not in base_cols + optional_cols]
        
        # 最终列顺序
        final_cols = base_cols + optional_cols + other_cols
        
        # 仅保留df中存在的列
        final_cols = [col for col in final_cols if col in df.columns]
        
        return df[final_cols] 