# 使用阿里云镜像源的Python 3.12.5镜像
FROM registry.cn-hangzhou.aliyuncs.com/library/python:3.12.5-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV FLASK_APP=app.py
ENV FLASK_ENV=production

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 配置pip使用国内镜像源并安装Python依赖
COPY requirements.txt .
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn && \
    pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p logs static/exports static/user_summary

# 设置权限
RUN chmod +x start_app.sh 2>/dev/null || true

# 暴露端口8667
EXPOSE 8667

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8667/ || exit 1

# 启动应用
CMD ["python", "app.py"]
