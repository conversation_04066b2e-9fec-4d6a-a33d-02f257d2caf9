{{# 
  var local = layui.data(layui.setter.tableName);
  
  //构建模版数据
  var admin = layui.admin;
  var data = {
    //功能菜单
    menu: [{
      name: '设置'
      ,list: [{
        name: '主题'
        ,alias: 'theme'
      }]
    },{
      name: '关于'
      ,list: [{
        name: '版本'
        ,alias: 'about'
      }]
    }]
  };
}}

<div class="layui-card-header">更多</div>
<div class="layui-card-body layadmin-about">
  <ul class="layadmin-menu-list">
  {{# layui.each(data.menu, function(i1, item1){ }}
    {{# layui.each(item1.list, function(i2, item2){ }}
    <li layadmin-event="about" data-alias="{{ item2.alias }}">
      <div class="layui-card">
        <div class="layui-card-header">
          <i class="layui-icon layui-icon-app"></i>
          <span>{{ item2.name }}</span>
        </div>
      </div>
    </li>
    {{# }); }}
  {{# }); }}
  </ul>
</div> 