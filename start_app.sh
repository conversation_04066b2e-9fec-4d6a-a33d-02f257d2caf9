#!/bin/bash

# ==========================================================
# SPCSP 应用启动脚本 (Nohup 后台版)
# chmod +x start_app.sh stop_app.sh
# ==========================================================

PROJECT_DIR="layuiAdmin_flask"
PID_FILE="layuiAdmin_flask.pid"

# 检查应用是否已在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "错误：应用似乎已在运行，进程ID为 $PID。"
        echo "如果需要重启，请先运行 'stop_app.sh'。"
        exit 1
    fi
fi

echo "[1/4] 检查并创建虚拟环境..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "创建虚拟环境失败！请确保已安装Python 3。"
        exit 1
    fi
fi

echo "[2/4] 激活虚拟环境..."
source venv/bin/activate

echo "[3/4] 安装依赖..."
pip install -r "$PROJECT_DIR/requirements.txt" --quiet
if [ $? -ne 0 ]; then
    echo "安装依赖失败！请检查网络和 requirements.txt 文件。"
    deactivate
    exit 1
fi

echo "[4/4] 正在后台启动应用..."
nohup python -u "$PROJECT_DIR/app.py" > app.log 2>&1 &
APP_PID=$!

# 将PID写入文件
echo $APP_PID > "$PID_FILE"

# 检查应用是否成功启动
sleep 2 # 等待2秒让应用有时间启动或失败
if ps -p $APP_PID > /dev/null; then
    echo "==============================================="
    echo "应用已成功在后台启动！"
    echo "进程ID (PID): $APP_PID"
    echo "日志文件: app.log"
    echo "==============================================="
else
    echo "==============================================="
    echo "错误：应用启动失败！请检查 'app.log' 获取详细信息。"
    echo "==============================================="
    rm "$PID_FILE" # 清理PID文件
fi

# 退出虚拟环境
deactivate 