#!/bin/bash

# LayuiAdmin Flask Docker 停止脚本

echo "=== 停止 LayuiAdmin Flask Docker 服务 ==="

# 停止并删除容器
if command -v docker-compose &> /dev/null; then
    echo "使用 docker-compose 停止服务..."
    docker-compose down
else
    echo "使用 docker compose 停止服务..."
    docker compose down
fi

echo "服务已停止"
echo ""
echo "如需完全清理（包括镜像），请运行:"
echo "docker-compose down --rmi all --volumes --remove-orphans"
echo "或"
echo "docker compose down --rmi all --volumes --remove-orphans"
