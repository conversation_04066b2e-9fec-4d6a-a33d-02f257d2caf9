<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>市公共算力服务平台-客户信息管理系统</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" href="{{ url_for('static', filename='img/logo.png') }}">
  <link href="{{ url_for('static', filename='res/layui/css/layui.css') }}" rel="stylesheet">
  <link href="{{ url_for('static', filename='res/adminui/dist/css/admin.css') }}" rel="stylesheet">
  <script>
  /^http(s*):\/\//.test(location.href) || alert('请通过 localhost 访问');
  </script>
</head>
<body class="layui-layout-body" id="LAY_home_iframe">
  <div id="LAY_app" style="visibility: hidden">
    <div class="layui-layout layui-layout-admin">
      <div class="layui-header">
        <!-- 头部区域 -->
        <ul class="layui-nav layui-layout-left">
          <li class="layui-nav-item layadmin-flexible" lay-unselect>
            <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
              <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
            </a>
          </li>
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="/" target="_blank" title="前台">
              <i class="layui-icon layui-icon-website"></i>
            </a>
          </li>
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;" layadmin-event="refresh" title="刷新">
              <i class="layui-icon layui-icon-refresh-3"></i>
            </a>
          </li>
        </ul>
        <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" layadmin-event="theme">
              <i class="layui-icon layui-icon-theme"></i>
            </a>
          </li>
          <li class="layui-nav-item layui-hide-xs" lay-unselect>
            <a href="javascript:;" layadmin-event="fullscreen">
              <i class="layui-icon layui-icon-screen-full"></i>
            </a>
          </li>
          <li class="layui-nav-item" lay-unselect>
            <a href="javascript:;">
              <cite>{{ user.username }} ({{ user.position }})</cite>
            </a>
            <dl class="layui-nav-child">
              <dd><a href="javascript:;" id="user-info-btn">基本资料</a></dd>
              <dd><a href="javascript:;" id="change-password-btn">修改密码</a></dd>
              <hr>
              <dd id="logoutBtn" style="text-align: center;"><a>退出</a></dd>
            </dl>
          </li>
        </ul>
      </div>

      <!-- 侧边菜单 -->
      <div class="layui-side layui-side-menu">
        <div class="layui-side-scroll">
          <div class="layui-logo" lay-href="/home/<USER>">
            <span>市公共算力服务平台</span>
          </div>

          <ul class="layui-nav layui-nav-tree" lay-accordion id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
            <li data-name="home" class="layui-nav-item layui-nav-itemed">
              <a href="javascript:;" lay-tips="主页" lay-direction="2">
                <i class="layui-icon layui-icon-home"></i>
                <cite>主页</cite>
              </a>
              <dl class="layui-nav-child">
                <dd data-name="console" class="layui-this">
                  <a lay-href="/home/<USER>">控制台</a>
                </dd>
              </dl>
            </li>
            <li data-name="user" class="layui-nav-item">
              <a href="javascript:;" lay-tips="客户" lay-direction="2">
                <i class="layui-icon layui-icon-user"></i>
                <cite>客户信息</cite>
              </a>
              <dl class="layui-nav-child">
                <dd>
                  <a lay-href="/user/data-summary">客户数据汇总</a>
                </dd>
              </dl>
            </li>

            {% if user.role == 'admin' %}
            <li data-name="management" class="layui-nav-item">
              <a href="javascript:;" lay-tips="系统管理" lay-direction="2">
                <i class="layui-icon layui-icon-set"></i>
                <cite>系统管理</cite>
              </a>
              <dl class="layui-nav-child">
                <dd>
                  <a lay-href="/user/management">用户管理</a>
                </dd>
              </dl>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>

      <!-- 页面标签 -->
      <div class="layadmin-pagetabs" id="LAY_app_tabs">
        <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
        <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
        <div class="layui-icon layadmin-tabs-control layui-icon-down">
          <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
            <li class="layui-nav-item" lay-unselect>
              <a href="javascript:;"></a>
              <dl class="layui-nav-child layui-anim-fadein">
                <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
              </dl>
            </li>
          </ul>
        </div>
        <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
          <ul class="layui-tab-title" id="LAY_app_tabsheader">
            <li lay-id="/home/<USER>" lay-attr="/home/<USER>" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
          </ul>
        </div>
      </div>

      <!-- 主体内容 -->
      <div class="layui-body" id="LAY_app_body">
        <div class="layadmin-tabsbody-item layui-show">
          <iframe src="/home/<USER>" frameborder="0" class="layadmin-iframe"></iframe>
        </div>
      </div>

      <!-- 辅助元素，一般用于移动设备下遮罩 -->
      <div class="layadmin-body-shade" layadmin-event="shade"></div>
    </div>
  </div>

  <script src="{{ url_for('static', filename='res/layui/layui.js') }}"></script>
  <script>
  layui.config({
    base: '{{ url_for("static", filename="res/") }}' // 静态资源所在路径
  }).use(['index', 'layer'], function(){
    var $ = layui.jquery;
    var layer = layui.layer;

    // Logout
    $('#logoutBtn').on('click', function(){
        $.ajax({
            url: '/api/auth/logout',
            type: 'POST',
            success: function(res){
                if(res.code === 0){
                    layer.msg('已注销', {icon: 1, time: 1000}, function(){
                        location.href = '/login';
                    });
                }
            }
        });
    });
    
    // 个人资料和修改密码事件绑定
    $('#user-info-btn').on('click', function(){
      layer.alert('职务: {{ user.position }}<br>正在完善中...', {
        title: '基本资料',
        icon: 1,
        skin: 'layui-layer-molv'
      });
    });

    $('#change-password-btn').on('click', function(){
      layer.open({
        type: 1,
        title: '修改密码',
        area: ['400px', '280px'],
        content: $('#change-password-template').html(),
        btn: ['确认修改', '取消'],
        yes: function(index, layero){
          var old_password = $('#old-password').val();
          var new_password = $('#new-password').val();
          var confirm_password = $('#confirm-password').val();

          if (!old_password || !new_password || !confirm_password) {
            layer.msg('所有字段均为必填项', {icon: 2});
            return;
          }
          if (new_password !== confirm_password) {
            layer.msg('两次输入的新密码不一致', {icon: 2});
            return;
          }
          
          $.ajax({
            url: '/api/auth/change-password',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
              old_password: old_password,
              new_password: new_password
            }),
            success: function(res) {
              if (res.code === 0) {
                layer.close(index);
                layer.msg(res.msg, {icon: 1});
              } else {
                layer.msg(res.msg, {icon: 2});
              }
            },
            error: function() {
              layer.msg('请求失败，请稍后再试', {icon: 2});
            }
          });
        }
      });
    });

  });
  </script>

  <script type="text/template" id="change-password-template">
    <div style="padding: 20px;">
      <form class="layui-form" action="">
        <div class="layui-form-item">
          <label class="layui-form-label">旧密码</label>
          <div class="layui-input-block">
            <input type="password" id="old-password" name="old_password" required lay-verify="required" placeholder="请输入旧密码" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">新密码</label>
          <div class="layui-input-block">
            <input type="password" id="new-password" name="new_password" required lay-verify="required" placeholder="请输入新密码" autocomplete="off" class="layui-input">
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">确认密码</label>
          <div class="layui-input-block">
            <input type="password" id="confirm-password" name="confirm_password" required lay-verify="required" placeholder="请再次输入新密码" autocomplete="off" class="layui-input">
          </div>
        </div>
      </form>
    </div>
  </script>
</body>
</html>