import json
import os
import threading
import sqlite3
import logging
from pathlib import Path

class AuthModel:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, db_path=None):
        if not hasattr(self, '_initialized'):
            # 如果没有指定数据库路径，则使用项目根目录下的users.db
            if db_path is None:
                # 获取当前文件的目录（models目录）
                current_dir = os.path.dirname(os.path.abspath(__file__))
                # 获取项目根目录（向上一级）
                project_root = os.path.dirname(current_dir)
                db_path = os.path.join(project_root, 'users.db')
            
            self.db_path = db_path
            self.json_path = os.path.join(os.path.dirname(self.db_path), 'users.json')  # 用于初始导入
            self._setup_logging()
            self._initialize_db()
            self._initialized = True

    def _setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger("auth_model")
        if not self.logger.handlers:
            # 确保logs目录存在
            logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
                
            handler = logging.FileHandler(os.path.join(logs_dir, "user_model.log"))
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def _initialize_db(self):
        """初始化SQLite数据库"""
        try:
            # 确保数据库目录存在
            db_dir = os.path.dirname(self.db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)

            # 连接到数据库并创建表
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    position TEXT,
                    role TEXT DEFAULT 'user'
                )
            ''')
            
            conn.commit()
            
            # 检查是否需要导入初始数据
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            if user_count == 0 and os.path.exists(self.json_path):
                self._import_users_from_json()
                
            conn.close()
            self.logger.info("数据库初始化成功")
        except Exception as e:
            self.logger.error(f"初始化数据库出错: {str(e)}")

    def _import_users_from_json(self):
        """从JSON文件导入用户数据"""
        try:
            with open(self.json_path, 'r', encoding='utf-8') as f:
                users = json.load(f)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for user in users:
                cursor.execute(
                    "INSERT INTO users (id, username, password, position, role) VALUES (?, ?, ?, ?, ?)",
                    (user['id'], user['username'], user['password'], user['position'], user['role'])
                )
            
            conn.commit()
            conn.close()
            self.logger.info(f"从JSON导入了 {len(users)} 个用户")
        except Exception as e:
            self.logger.error(f"从JSON导入用户出错: {str(e)}")

    def get_all_users(self):
        """获取所有用户（不返回密码）"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, username, position, role FROM users")
            users = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return users
        except Exception as e:
            self.logger.error(f"获取所有用户出错: {str(e)}")
            return []

    def find_user_by_username(self, username):
        """通过用户名查找用户"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            user = cursor.fetchone()
            
            conn.close()
            return dict(user) if user else None
        except Exception as e:
            self.logger.error(f"查找用户出错: {str(e)}")
            return None

    def verify_user(self, username, password):
        """验证用户登录"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM users WHERE username = ? AND password = ?", 
                          (username, password))
            user = cursor.fetchone()
            
            conn.close()
            return dict(user) if user else None
        except Exception as e:
            self.logger.error(f"验证用户出错: {str(e)}")
            return None

    def add_user(self, data):
        """添加新用户"""
        try:
            if self.find_user_by_username(data['username']):
                return False, "用户已存在"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取最大ID
            cursor.execute("SELECT MAX(id) FROM users")
            max_id = cursor.fetchone()[0] or 0
            new_id = max_id + 1
            
            cursor.execute(
                "INSERT INTO users (id, username, password, position, role) VALUES (?, ?, ?, ?, ?)",
                (new_id, data['username'], data['password'], data['position'], data.get('role', 'user'))
            )
            
            conn.commit()
            conn.close()
            return True, "添加成功"
        except Exception as e:
            self.logger.error(f"添加用户出错: {str(e)}")
            return False, f"添加失败: {str(e)}"

    def update_user(self, username, data):
        """更新用户信息"""
        try:
            if not self.find_user_by_username(username):
                return False, "用户不存在"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "UPDATE users SET position = ?, role = ? WHERE username = ?",
                (data.get('position'), data.get('role'), username)
            )
            
            conn.commit()
            conn.close()
            return True, "更新成功"
        except Exception as e:
            self.logger.error(f"更新用户出错: {str(e)}")
            return False, f"更新失败: {str(e)}"

    def delete_user(self, username):
        """删除用户"""
        try:
            user = self.find_user_by_username(username)
            if not user:
                return False, "用户不存在"
            if user['role'] == 'admin':
                return False, "不能删除管理员"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM users WHERE username = ?", (username,))
            
            conn.commit()
            conn.close()
            return True, "删除成功"
        except Exception as e:
            self.logger.error(f"删除用户出错: {str(e)}")
            return False, f"删除失败: {str(e)}"

    def reset_password(self, username, new_password):
        """重置用户密码"""
        try:
            if not self.find_user_by_username(username):
                return False, "用户不存在"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "UPDATE users SET password = ? WHERE username = ?",
                (new_password, username)
            )
            
            conn.commit()
            conn.close()
            return True, "密码重置成功"
        except Exception as e:
            self.logger.error(f"重置密码出错: {str(e)}")
            return False, f"重置失败: {str(e)}"

    def change_password(self, username, old_password, new_password):
        """修改用户密码"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            user = cursor.fetchone()
            
            if not user:
                conn.close()
                return False, "用户不存在"
            
            if user['password'] != old_password:
                conn.close()
                return False, "原密码验证失败，请重新输入。"
            
            cursor.execute(
                "UPDATE users SET password = ? WHERE username = ?",
                (new_password, username)
            )
            
            conn.commit()
            conn.close()
            return True, "密码修改成功"
        except Exception as e:
            self.logger.error(f"修改密码出错: {str(e)}")
            return False, f"修改失败: {str(e)}" 