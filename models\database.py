#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DuckDB 数据库连接模块
实现连接池管理以支持并发web请求
"""

import os
import json
import logging
import threading
import time
import collections
import duckdb
import re
from pathlib import Path

# 单例模式实现数据库连接管理
class DatabaseManager:
    _instance = None
    _lock = threading.Lock()
    
    @classmethod
    def get_instance(cls, config=None):
        """获取单例实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = DatabaseManager(config)
            return cls._instance
    
    def __init__(self, config=None):
        """初始化数据库管理器"""
        if config is None:
            config = self._load_config()
        
        self.config = config
        # 获取项目根目录的绝对路径
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.db_path = os.path.join(root_dir, 'bills.duckdb')
        print(f"数据库路径: {self.db_path}")
        
        self.read_only = self.config.get('read_only', True)  # 默认只读模式
        self.max_connections = self.config.get('max_connections', 10)  # 默认最大连接数
        
        # 连接池 - 使用OrderedDict实现LRU缓存
        self._connections = collections.OrderedDict()
        self._connection_times = {}  # 记录每个连接的最后访问时间
        self._connection_semaphore = threading.Semaphore(self.max_connections)
        
        # 设置日志
        self._setup_logging()
        self.logger.info(f"DatabaseManager 初始化，数据库路径: {self.db_path}, 最大连接数: {self.max_connections}")
        
        # 创建初始连接(用于共享配置)
        self._master_conn = self._create_connection()
        
    def _setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger("duckdb")
        if not self.logger.handlers:
            # 确保logs目录存在
            logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
                
            handler = logging.FileHandler(os.path.join(logs_dir, "db.log"))
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_config(self):
        """加载配置"""
        # 获取项目根目录的绝对路径
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        config = {
            'db_path': os.path.join(root_dir, 'bills.duckdb'),
            'read_only': True,
            'max_connections': 10,
            'memory_limit': '2GB',
            'threads': 4,
            'temp_directory': os.path.join(root_dir, '.tmp'),
            'force_compression': 'none',
        }
        
        try:
            # 尝试从项目根目录加载配置
            config_path = os.path.join(root_dir, "config.json")
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    user_config = json.load(f)
                    # 如果有database键，则获取其配置
                    db_config = user_config.get("database", {})
                    # 使用用户配置更新默认配置
                    config.update(db_config)
                    
                    # 确保使用正确的数据库文件路径
                    if 'db_path' in db_config and not os.path.isabs(db_config['db_path']):
                        config['db_path'] = os.path.join(root_dir, db_config['db_path'])
        except Exception as e:
            print(f"加载数据库配置出错: {str(e)}")
            
        return config
    
    def _create_connection(self):
        """创建新的数据库连接"""
        try:
            # 连接到DuckDB
            conn = duckdb.connect(self.db_path, read_only=self.read_only)
            
            # 创建临时目录
            temp_dir = self.config.get('temp_directory', '../../.tmp')
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir, exist_ok=True)
            
            # 应用优化配置
            conn.execute(f"PRAGMA memory_limit='{self.config.get('memory_limit', '2GB')}'")
            conn.execute(f"PRAGMA threads={self.config.get('threads', 4)}")
            conn.execute(f"PRAGMA force_compression='{self.config.get('force_compression', 'none')}'")
            conn.execute(f"PRAGMA temp_directory='{temp_dir}'")
            
            # 启用对象缓存
            conn.execute("PRAGMA enable_object_cache")
            
            return conn
        except Exception as e:
            self.logger.error(f"创建数据库连接出错: {str(e)}")
            return None
    
    def get_connection(self):
        """从连接池获取连接，如果达到最大连接数则关闭最早的连接"""
        thread_id = threading.get_ident()
        
        with self._lock:
            # 检查当前线程是否已有连接
            if thread_id in self._connections:
                # 更新访问时间
                conn = self._connections[thread_id]
                self._connection_times[thread_id] = time.time()
                # 将此连接移到OrderedDict的末尾（表示最近使用）
                self._connections.move_to_end(thread_id)
                
                # 测试连接是否有效
                try:
                    conn.execute("SELECT 1")
                    return conn
                except Exception as e:
                    self.logger.warning(f"连接失效，将重新创建: {str(e)}")
                    try:
                        conn.close()
                    except:
                        pass
                    del self._connections[thread_id]
                    if thread_id in self._connection_times:
                        del self._connection_times[thread_id]
            
            # 检查是否达到最大连接数
            if len(self._connections) >= self.max_connections:
                # 关闭最早的连接（OrderedDict的第一个元素）
                oldest_thread_id, oldest_conn = next(iter(self._connections.items()))
                self.logger.info(f"达到最大连接数 {self.max_connections}，关闭最早的连接 (线程ID: {oldest_thread_id})")
                try:
                    oldest_conn.close()
                except Exception as e:
                    self.logger.error(f"关闭旧连接出错: {str(e)}")
                
                # 从连接池中移除
                del self._connections[oldest_thread_id]
                if oldest_thread_id in self._connection_times:
                    del self._connection_times[oldest_thread_id]
            
            # 创建新连接
            conn = self._create_connection()
            if conn:
                self._connections[thread_id] = conn
                self._connection_times[thread_id] = time.time()
                return conn
            else:
                self.logger.error("创建新连接失败")
                return None
    
    def release_connection(self, conn=None):
        """标记连接为可重用（不实际关闭）"""
        thread_id = threading.get_ident()
        with self._lock:
            if thread_id in self._connections:
                self._connection_times[thread_id] = time.time()
                # 将此连接移到OrderedDict的末尾（表示最近使用）
                self._connections.move_to_end(thread_id)
    
    def get_connection_count(self):
        """获取当前连接数"""
        with self._lock:
            return len(self._connections)
    
    def get_connection_info(self):
        """获取连接池信息"""
        with self._lock:
            info = {
                "max_connections": self.max_connections,
                "current_connections": len(self._connections),
                "connections": []
            }
            
            current_time = time.time()
            for thread_id, conn in self._connections.items():
                last_access = self._connection_times.get(thread_id, 0)
                info["connections"].append({
                    "thread_id": thread_id,
                    "last_access": last_access,
                    "idle_time": current_time - last_access
                })
            
            return info
    
    def close_all_connections(self):
        """关闭所有连接"""
        with self._lock:
            # 关闭主连接
            if hasattr(self, '_master_conn') and self._master_conn:
                try:
                    self._master_conn.close()
                except:
                    pass
            
            # 关闭所有线程连接
            for conn in self._connections.values():
                try:
                    conn.close()
                except:
                    pass
            self._connections.clear()
            
    def execute_query(self, sql, params=None):
        """执行SQL查询并返回DataFrame"""
        conn = self.get_connection()
        if not conn:
            return None
            
        try:
            result = conn.execute(sql, params).fetchdf() if params else conn.execute(sql).fetchdf()
            self.release_connection()
            return result
        except Exception as e:
            self.logger.error(f"查询执行错误: {str(e)}\nSQL: {sql}")
            self.release_connection()
            return None
            
    def execute_and_fetch(self, sql, params=None):
        """执行SQL查询并返回结果行"""
        conn = self.get_connection()
        if not conn:
            return []
            
        try:
            result = conn.execute(sql, params).fetchall() if params else conn.execute(sql).fetchall()
            self.release_connection()
            return result
        except Exception as e:
            self.logger.error(f"查询执行错误: {str(e)}\nSQL: {sql}")
            self.release_connection()
            return []
    
    def database_exists(self):
        """检查数据库文件是否存在"""
        return os.path.exists(self.db_path) and os.path.getsize(self.db_path) > 0
    
    def create_materialized_view_if_not_exists(self, view_name, sql_query):
        """创建物化视图（如果需要写入权限）"""
        if self.read_only:
            self.logger.warning(f"数据库处于只读模式，无法创建物化视图 {view_name}")
            return False
            
        conn = self.get_connection()
        if not conn:
            return False
            
        try:
            # 检查表是否存在
            tables = conn.execute("SHOW TABLES").fetchall()
            if any(table[0] == view_name for table in tables):
                # 删除现有物化视图
                conn.execute(f"DROP TABLE IF EXISTS {view_name}")
                
            # 创建物化视图（实际上是表）
            conn.execute(f"CREATE TABLE {view_name} AS {sql_query}")
            self.logger.info(f"已创建/更新物化视图: {view_name}")
            self.release_connection()
            return True
        except Exception as e:
            self.logger.error(f"创建物化视图出错: {str(e)}")
            self.release_connection()
            return False

# 上下文管理器，用于自动获取和释放数据库连接
class DBConnection:
    def __init__(self, db_manager=None):
        if db_manager is None:
            self.db_manager = DatabaseManager.get_instance()
        else:
            self.db_manager = db_manager
        self.conn = None
    
    def __enter__(self):
        self.conn = self.db_manager.get_connection()
        return self.conn
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.db_manager.release_connection()
        return False  # 不抑制异常

# 添加主项目中的DatabaseConnection类，用于兼容现有代码
class DatabaseConnection:
    _instance = None
    _lock = threading.Lock()
    _connections = {}  # 线程ID到连接的映射

    @classmethod
    def get_instance(cls, db_path="bills.duckdb", config=None):
        """单例模式获取数据库连接实例"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = DatabaseConnection(db_path, config)
            return cls._instance

    def __init__(self, db_path="bills.duckdb", config=None):
        # 获取绝对路径
        if not os.path.isabs(db_path):
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            db_path = os.path.join(root_dir, db_path)
            
        self.db_path = db_path
        self.conn = None
        self.config = config or self._load_default_config()
        self.logger = logging.getLogger("db_connection")
        self.connect()
        
    def _load_default_config(self):
        """加载默认配置"""
        default_config = {
            "memory_limit": "2GB",
            "threads": 4,
            "force_compression": "none",
            "temp_directory": "./.tmp",
            "use_connection_pool": True,
            "enable_wal": False
        }
        
        try:
            # 获取项目根目录的绝对路径
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = os.path.join(root_dir, "config.json")
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    user_config = json.load(f)
                    # 如果有database键，则获取其配置
                    db_config = user_config.get("database", {})
                    # 使用用户配置更新默认配置
                    default_config.update(db_config)
        except Exception as e:
            print(f"加载数据库配置出错: {str(e)}")
            
        return default_config
        
    def connect(self):
        """连接到DuckDB数据库"""
        try:
            # 无论数据库文件是否存在，都尝试连接
            # DuckDB会在需要时自动创建数据库文件
            self.conn = duckdb.connect(self.db_path)
            
            # 创建临时目录
            temp_dir = self.config.get("temp_directory", "./.tmp")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir, exist_ok=True)
            
            # 性能优化配置
            # 增加内存限制以提高性能 (根据配置)
            self.conn.execute(f"PRAGMA memory_limit='{self.config.get('memory_limit', '2GB')}'")
            # 启用多线程执行
            self.conn.execute(f"PRAGMA threads={self.config.get('threads', 4)}")
            # 优化缓存
            self.conn.execute(f"PRAGMA force_compression='{self.config.get('force_compression', 'none')}'")
            # 增加临时目录空间
            self.conn.execute(f"PRAGMA temp_directory='{temp_dir}'")
            
            # 启用WAL模式提高写入性能
            if self.config.get("enable_wal", False):
                self.conn.execute("PRAGMA journal_mode=WAL")
            
            # 使用预加载技术
            self.conn.execute("PRAGMA enable_object_cache")
            
            return True
        except Exception as e:
            print(f"无法连接到数据库: {str(e)}")
            self.conn = None
            return False
    
    def get_connection(self):
        """获取当前线程的数据库连接（连接池模式）"""
        if not self.config.get("use_connection_pool", True):
            return self.conn
            
        thread_id = threading.get_ident()
        with self._lock:
            if thread_id not in self._connections:
                # 为线程创建新连接
                conn = duckdb.connect(self.db_path)
                
                # 复制主连接的配置
                conn.execute(f"PRAGMA memory_limit='{self.config.get('memory_limit', '2GB')}'")
                conn.execute(f"PRAGMA threads={self.config.get('threads', 4)}")
                conn.execute(f"PRAGMA force_compression='{self.config.get('force_compression', 'none')}'")
                conn.execute(f"PRAGMA temp_directory='{self.config.get('temp_directory', './.tmp')}'")
                
                self._connections[thread_id] = conn
                
            return self._connections[thread_id]
            
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
        
        # 关闭所有线程连接
        with self._lock:
            for conn in self._connections.values():
                conn.close()
            self._connections.clear()
            
    def execute_query(self, sql):
        """执行SQL查询并返回DataFrame"""
        conn = self.get_connection()  
        if not conn:
            return None
            
        try:
            return conn.execute(sql).fetchdf()
        except Exception as e:
            self.logger.error(f"执行查询出错: {str(e)}")
            return None
            
    def execute_query_with_params(self, sql, params=None):
        """执行带参数的SQL查询并返回DataFrame"""
        conn = self.get_connection()
        if not conn:
            return None
            
        try:
            if params:
                return conn.execute(sql, params).fetchdf()
            else:
                return conn.execute(sql).fetchdf()
        except Exception as e:
            self.logger.error(f"执行参数查询出错: {str(e)}")
            return None
            
    def database_exists(self):
        """检查数据库文件是否存在"""
        return os.path.exists(self.db_path) and os.path.getsize(self.db_path) > 0
        
    def has_data(self):
        """检查数据库是否包含数据"""
        conn = self.get_connection()
        if not conn:
            return False
            
        try:
            # 检查transaction_details表是否存在
            tables = conn.execute("SHOW TABLES").fetchall()
            if not any(table[0] == 'transaction_details' for table in tables):
                return False
                
            # 检查transaction_details表是否有数据
            count = conn.execute("SELECT COUNT(*) FROM transaction_details").fetchone()[0]
            return count > 0
        except Exception as e:
            self.logger.error(f"检查数据库数据时出错: {str(e)}")
            return False
            
    def optimize_database(self):
        """优化数据库性能"""
        conn = self.get_connection()
        if not conn:
            return False
            
        try:
            # 分析表以更新统计信息
            conn.execute("ANALYZE")
            # 优化存储
            conn.execute("VACUUM")
            # 检查数据库完整性
            conn.execute("PRAGMA force_checkpoint")
            return True
        except Exception as e:
            self.logger.error(f"优化数据库时出错: {str(e)}")
            return False
    
    def create_view_if_not_exists(self, view_name, sql_query):
        """创建或替换视图"""
        conn = self.get_connection()
        if not conn:
            return False
            
        try:
            # 检查视图是否存在
            tables = conn.execute("SHOW TABLES").fetchall()
            if any(table[0] == view_name for table in tables):
                # 删除现有视图
                conn.execute(f"DROP VIEW IF EXISTS {view_name}")
                
            # 创建新视图
            conn.execute(f"CREATE VIEW {view_name} AS {sql_query}")
            self.logger.info(f"已创建/更新视图: {view_name}")
            return True
        except Exception as e:
            self.logger.error(f"创建视图出错: {str(e)}")
            return False
    
    def create_materialized_view_if_not_exists(self, view_name, sql_query):
        """创建物化视图提升查询性能"""
        conn = self.get_connection()
        if not conn:
            return False
            
        try:
            # 检查表是否存在
            tables = conn.execute("SHOW TABLES").fetchall()
            if any(table[0] == view_name for table in tables):
                # 删除现有物化视图
                conn.execute(f"DROP TABLE IF EXISTS {view_name}")
                
            # 创建物化视图（实际上是表）
            conn.execute(f"CREATE TABLE {view_name} AS {sql_query}")
            self.logger.info(f"已创建/更新物化视图: {view_name}")
            return True
        except Exception as e:
            self.logger.error(f"创建物化视图出错: {str(e)}")
            return False 