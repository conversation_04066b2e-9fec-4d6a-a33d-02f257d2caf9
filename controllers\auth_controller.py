from flask import Blueprint, request, jsonify, session
from models.auth_model import AuthModel

blueprint = Blueprint('auth', __name__, url_prefix='/api/auth')
auth_model = AuthModel()

@blueprint.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({"code": 400, "msg": "请输入用户名和密码"}), 400

    user = auth_model.verify_user(username, password)

    if user:
        session['user'] = user
        return jsonify({"code": 0, "msg": "登录成功", "data": user})
    else:
        return jsonify({"code": 401, "msg": "用户名或密码不正确"})

@blueprint.route('/logout', methods=['POST'])
def logout():
    session.pop('user', None)
    return jsonify({"code": 0, "msg": "已成功注销"})

@blueprint.route('/session', methods=['GET'])
def get_session():
    user = session.get('user')
    if user:
        return jsonify({"code": 0, "data": user})
    else:
        return jsonify({"code": 401, "msg": "未登录"}), 401

@blueprint.route('/change-password', methods=['POST'])
def change_password():
    user_session = session.get('user')
    if not user_session:
        return jsonify({"code": 401, "msg": "请先登录"}), 401

    data = request.get_json()
    old_password = data.get('old_password')
    new_password = data.get('new_password')
    
    if not old_password or not new_password:
        return jsonify({"code": 400, "msg": "密码不能为空"}), 400

    username = user_session['username']
    success, message = auth_model.change_password(username, old_password, new_password)

    if success:
        return jsonify({"code": 0, "msg": message})
    else:
        return jsonify({"code": 500, "msg": message}) 