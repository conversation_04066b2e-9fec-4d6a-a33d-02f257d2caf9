@echo off
echo ===============================================
echo SPCSP应用启动脚本 - Windows版
echo ===============================================

:: 检查是否已存在虚拟环境
if not exist venv (
    echo [1/4] 正在创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo 创建虚拟环境失败！请确保已安装Python。
        pause
        exit /b 1
    )
) else (
    echo [1/4] 检测到已存在的虚拟环境。
)

:: 激活虚拟环境
echo [2/4] 正在激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo 激活虚拟环境失败！
    pause
    exit /b 1
)

:: 安装依赖
echo [3/4] 正在安装依赖包...
pip install -r layuiAdmin_flask\requirements.txt
if %errorlevel% neq 0 (
    echo 安装依赖失败！请检查网络连接和requirements.txt文件。
    pause
    exit /b 1
)

:: 启动应用
echo [4/4] 正在启动应用...
echo ===============================================
echo 应用已启动，按Ctrl+C可停止服务
echo ===============================================
python layuiAdmin_flask\app.py 