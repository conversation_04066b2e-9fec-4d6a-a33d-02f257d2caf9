#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户作业控制器
处理用户作业记录相关的请求
"""

import traceback
import pandas as pd
from flask import jsonify, request
from .base import UserControllerBase

class UserJobsController(UserControllerBase):
    """用户作业控制器"""
    
    def __init__(self):
        """初始化用户作业控制器"""
        super().__init__()
    
    def get_user_jobs(self):
        """获取用户作业记录API - 默认每页10条，支持10/20/50/100条每页"""
        try:
            # 获取请求参数
            username = request.args.get('username', '')
            date_range = request.args.get('date-range', '')
            page = request.args.get('page', default=1, type=int)
            limit = request.args.get('limit', default=10, type=int)
            
            if not username:
                return jsonify({
                    "code": 1,
                    "msg": "用户名不能为空",
                    "count": 0,
                    "data": []
                })
            
            # 解析日期范围
            start_date = ""
            end_date = ""
            if date_range:
                try:
                    date_parts = date_range.split(' - ')
                    if len(date_parts) == 2:
                        start_date, end_date = date_parts
                except Exception as e:
                    print(f"解析日期范围出错: {e}")
            
            # 获取用户作业记录
            # 使用本地UserModel类
            jobs_data = self.user_model.get_user_jobs(username, "", start_date, end_date)
            
            # 处理结果
            if jobs_data is not None and not jobs_data.empty:
                # 计算分页
                total_count = len(jobs_data)
                total_pages = max(1, (total_count + limit - 1) // limit)
                
                # 获取当前页数据
                start_idx = (page - 1) * limit
                end_idx = min(start_idx + limit, total_count)
                current_page_data = jobs_data.iloc[start_idx:end_idx].copy() if total_count > 0 else None
                
                # 转换DataFrame为JSON友好格式
                jobs_list = []
                if current_page_data is not None and not current_page_data.empty:
                    # 删除"运行时长(秒)"列，只用于后端计算
                    if "运行时长(秒)" in current_page_data.columns:
                        current_page_data = current_page_data.drop(columns=["运行时长(秒)"])
                    
                    for idx, row in current_page_data.iterrows():
                        item = {}
                        for col in current_page_data.columns:
                            value = row[col]
                            if pd.isna(value):
                                item[col] = ""
                            elif isinstance(value, pd.Timestamp):
                                item[col] = value.strftime("%Y-%m-%d %H:%M:%S")
                            else:
                                item[col] = str(value)
                        jobs_list.append(item)
                
                # 计算总运行时长
                total_seconds = 0
                if "运行时长(秒)" in jobs_data.columns:
                    total_seconds = jobs_data["运行时长(秒)"].sum()
                
                # 格式化总运行时长
                total_duration = self.user_model._format_seconds_to_duration(total_seconds)
                
                return jsonify({
                    "code": 0,
                    "msg": "",
                    "count": total_count,
                    "total_pages": total_pages,
                    "total_duration": total_duration,
                    "data": jobs_list
                })
            else:
                return jsonify({
                    "code": 0,
                    "msg": "没有找到作业记录",
                    "count": 0,
                    "total_pages": 1,
                    "total_duration": "0秒",
                    "data": []
                })
                
        except Exception as e:
            print(f"获取用户作业记录出错: {e}")
            traceback.print_exc()
            return jsonify({
                "code": 500,
                "msg": f"获取用户作业记录出错: {str(e)}",
                "data": None
            }) 