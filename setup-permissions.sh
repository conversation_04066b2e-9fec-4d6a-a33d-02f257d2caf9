#!/bin/bash

# 设置Docker部署脚本权限

echo "设置脚本执行权限..."

chmod +x docker-start.sh
chmod +x docker-stop.sh
chmod +x setup-permissions.sh
chmod +x configure-docker-mirror.sh

echo "权限设置完成！"
echo ""
echo "可用命令:"
echo "  ./configure-docker-mirror.sh  # 配置Docker国内镜像源（需要root权限）"
echo "  ./docker-start.sh             # 启动服务"
echo "  ./docker-stop.sh              # 停止服务"
echo ""
echo "如果遇到网络问题，请先运行："
echo "  sudo ./configure-docker-mirror.sh"
