#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户汇总数据相关功能
"""

import traceback
from .base import query_cache

class UserSummaryMixin:
    """用户汇总数据相关功能"""
    
    @query_cache(ttl_seconds=300)
    def get_user_summary(self, search_term="", date_filter=""):
        """获取用户汇总数据"""
        try:
            print(f"开始获取用户汇总数据: search_term={search_term}, date_filter={date_filter}")
            
            # 检查transaction_details表是否存在
            if not self._check_table_exists("transaction_details"):
                self.logger.error("交易明细表不存在")
                return None
            
            # 首先检查是否有物化视图
            if self._check_materialized_view_exists("user_summary_mv") and not date_filter:
                print("使用物化视图查询")
                # 使用物化视图查询
                search_condition = ""
                params = []
                
                if search_term:
                    search_condition = ' WHERE ("用户名" LIKE ? OR "客户名称" LIKE ?)'
                    params = [f'%{search_term}%', f'%{search_term}%']
                
                # 从物化视图查询
                summary_sql = f"""
                SELECT * FROM user_summary_mv
                {search_condition}
                ORDER BY 总支出金额 DESC
                """
                
                print(f"执行SQL: {summary_sql}, 参数: {params}")
                result = self.db_manager.execute_query(summary_sql, params)
                
                # 如果有数据，添加OBS容量和作业数
                if result is not None and not result.empty:
                    result = self.add_obs_storage_to_summary(result)
                    result = self.add_cumulative_jobs_to_summary(result)
                    # 添加共享卡时数据
                    result = self.add_shared_card_hours_to_summary(result, "", "")
                    # 添加专属卡时数据
                    result = self.add_exclusive_card_hours_to_summary(result, "", "")
                    
                    # 重新排列列顺序
                    result = self._reorder_summary_columns(result)
                    
                return result
            
            # 使用统一查询方法
            return self.get_user_summary_unified(search_term=search_term, end_date=date_filter)
            
        except Exception as e:
            self.logger.error(f"获取用户汇总数据时出错: {str(e)}")
            traceback.print_exc()
            return None
    
    @query_cache(ttl_seconds=300)
    def get_user_summary_by_date_range(self, search_term="", start_date="", end_date=""):
        """根据日期范围获取用户汇总数据"""
        try:
            print(f"按日期范围获取用户汇总数据: search_term={search_term}, start_date={start_date}, end_date={end_date}")
            
            # 检查transaction_details表是否存在
            if not self._check_table_exists("transaction_details"):
                self.logger.error("交易明细表不存在")
                return None
            
            # 使用统一查询方法
            return self.get_user_summary_unified(search_term=search_term, start_date=start_date, end_date=end_date)
            
        except Exception as e:
            self.logger.error(f"按日期范围获取用户汇总数据时出错: {str(e)}")
            traceback.print_exc()
            return None

    def get_user_summary_unified(self, search_term="", start_date="", end_date=""):
        """统一的用户汇总数据查询方法
        
        Args:
            search_term: 搜索关键词
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            DataFrame: 包含用户汇总数据的数据框
        """
        try:
            params = []
            
            # 构建日期条件
            date_conditions = []
            if start_date:
                date_conditions.append('t."交易时间" >= ?')
                params.append(f"{start_date} 00:00:00" if not start_date.endswith("00:00:00") else start_date)
            if end_date:
                date_conditions.append('t."交易时间" <= ?')
                params.append(f"{end_date} 23:59:59" if not end_date.endswith("23:59:59") else end_date)
            
            date_condition = " AND ".join(date_conditions)
            
            # 搜索条件
            search_condition = ""
            if search_term:
                search_condition = ' AND (t."用户名" LIKE ? OR t."客户名称" LIKE ?)'
                params.extend([f'%{search_term}%', f'%{search_term}%'])
            
            # 使用简化的SQL - 基础部分
            base_sql = """
                SELECT 
                    t."用户名",
                    t."客户名称",
                SUM(CASE WHEN t."收支类型" = '支出' THEN CAST(t."金额(元)" AS DOUBLE) ELSE 0.0 END) AS 总支出金额,
                SUM(CASE WHEN t."收支类型" = '收入' THEN CAST(t."金额(元)" AS DOUBLE) ELSE 0.0 END) AS 总收入金额,
                SUM(CASE WHEN t."收支类型" = '收入' THEN CAST(t."金额(元)" AS DOUBLE) ELSE -CAST(t."金额(元)" AS DOUBLE) END) AS 净收支,
                COUNT(*) AS 交易总次数,
                COUNT(CASE WHEN t."收支类型" = '支出' THEN 1 END) AS 支出次数,
                COUNT(CASE WHEN t."收支类型" = '收入' THEN 1 END) AS 收入次数,
                MIN(t."交易时间") AS 最早交易时间,
                MAX(t."交易时间") AS 最近交易时间,
            """
                
            # 现金余额子查询 
            if end_date:
                balance_subquery = f"""
                (SELECT CAST(tx."现金余额(元)" AS DOUBLE) FROM transaction_details tx 
                 WHERE tx."用户名" = t."用户名" AND tx."客户名称" = t."客户名称" 
                 AND tx."交易时间" <= '{end_date} 23:59:59'
                 ORDER BY tx."交易时间" DESC, tx."交易编号" DESC LIMIT 1) AS 当前现金余额,
                """
            else:
                balance_subquery = """
                (SELECT CAST(tx."现金余额(元)" AS DOUBLE) FROM transaction_details tx 
                 WHERE tx."用户名" = t."用户名" AND tx."客户名称" = t."客户名称" 
                 ORDER BY tx."交易时间" DESC, tx."交易编号" DESC LIMIT 1) AS 当前现金余额,
                """
                
            # 信用额度子查询
            if end_date:
                credit_subquery = f"""
                (SELECT CAST(tx."信用额度(元)" AS DOUBLE) FROM transaction_details tx 
                 WHERE tx."用户名" = t."用户名" AND tx."客户名称" = t."客户名称" 
                 AND tx."交易时间" <= '{end_date} 23:59:59'
                 ORDER BY tx."交易时间" DESC, tx."交易编号" DESC LIMIT 1) AS 当前信用额度
                """
            else:
                credit_subquery = """
                (SELECT CAST(tx."信用额度(元)" AS DOUBLE) FROM transaction_details tx 
                 WHERE tx."用户名" = t."用户名" AND tx."客户名称" = t."客户名称" 
                 ORDER BY tx."交易时间" DESC, tx."交易编号" DESC LIMIT 1) AS 当前信用额度
                """
                
            # 组合完整SQL
            search_sql = base_sql + balance_subquery + credit_subquery + f"""
                FROM transaction_details t
                WHERE {date_condition or "1=1"}{search_condition}
            GROUP BY t."用户名", t."客户名称"
            ORDER BY 总支出金额 DESC
            """
            
            print(f"执行SQL: {search_sql}, 参数: {params}")
            result = self.db_manager.execute_query(search_sql, params)
            
            # 如果有数据，添加其他信息
            if result is not None and not result.empty:
                print(f"查询结果: {result.shape}")
                result = self.add_obs_storage_to_summary(result, end_date)
                result = self.add_cumulative_jobs_to_summary(result, end_date, start_date)
                
                # 添加共享卡时数据
                result = self.add_shared_card_hours_to_summary(result, start_date, end_date)
                
                # 添加专属卡时数据
                result = self.add_exclusive_card_hours_to_summary(result, start_date, end_date)
                
                # 添加专属卡时(除误差)数据
                result = self.add_exclusive_card_hours_adjusted_to_summary(result, start_date, end_date)
                
                # 重新排列列顺序
                result = self._reorder_summary_columns(result)
            else:
                print("查询结果为空")
                
            return result
            
        except Exception as e:
            self.logger.error(f"统一获取用户汇总数据时出错: {str(e)}")
            traceback.print_exc()
            return None
    
    @query_cache(ttl_seconds=300)
    def get_total_records_count(self, date_filter="", search_term=""):
        """获取总记录数"""
        try:
            # 构建参数和条件
            params = []
            conditions = []
            
            if date_filter:
                conditions.append('t."交易时间" <= ?')
                params.append(date_filter)
                
            if search_term:
                conditions.append('(t."用户名" LIKE ? OR t."客户名称" LIKE ?)')
                params.extend([f'%{search_term}%', f'%{search_term}%'])
                
            # 构建SQL查询
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            count_sql = f"""
            SELECT COUNT(*) AS total_count
            FROM transaction_details t
            WHERE {where_clause}
            """
            
            result = self.db_manager.execute_query(count_sql, params)
            if result is not None and not result.empty:
                return result.iloc[0, 0]
            return 0
            
        except Exception as e:
            self.logger.error(f"获取总记录数时出错: {str(e)}")
            return 0
    
    @query_cache(ttl_seconds=300)
    def get_earliest_transaction_date(self):
        """获取最早交易日期"""
        try:
            date_sql = """
            SELECT MIN(CAST("交易时间" AS DATE)) AS earliest_date
            FROM transaction_details
            """
            
            result = self.db_manager.execute_query(date_sql)
            if result is not None and not result.empty:
                date_val = result.iloc[0, 0]
                return date_val.strftime("%Y-%m-%d") if date_val else None
            return None
            
        except Exception as e:
            self.logger.error(f"获取最早交易日期时出错: {str(e)}")
            return None
    
    @query_cache(ttl_seconds=300)
    def get_latest_transaction_date(self):
        """获取最晚交易日期"""
        try:
            date_sql = """
            SELECT MAX(CAST("交易时间" AS DATE)) AS latest_date
            FROM transaction_details
            """
            
            result = self.db_manager.execute_query(date_sql)
            if result is not None and not result.empty:
                date_val = result.iloc[0, 0]
                return date_val.strftime("%Y-%m-%d") if date_val else None
            return None
            
        except Exception as e:
            self.logger.error(f"获取最晚交易日期时出错: {str(e)}")
            return None
    
    @query_cache(ttl_seconds=300)
    def get_default_date_range(self):
        """获取默认日期范围"""
        try:
            # 获取最早和最晚交易日期
            earliest_date = self.get_earliest_transaction_date()
            latest_date = self.get_latest_transaction_date()
            
            # 返回日期范围
            return {
                "earliest_date": earliest_date,
                "latest_date": latest_date
            }
            
        except Exception as e:
            self.logger.error(f"获取默认日期范围时出错: {str(e)}")
            return {
                "earliest_date": None,
                "latest_date": None
            } 