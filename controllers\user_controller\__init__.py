#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户控制器
处理用户相关的请求
该文件作为入口，引用子模块中的功能
"""

from flask import Blueprint
import traceback

# 导入子模块
from .user_data import UserDataController
from .user_transactions import UserTransactionsController
from .user_jobs import UserJobsController
from .export import ExportController
from .dashboard import DashboardController
from .fragments import FragmentsController

class UserController:
    def __init__(self):
        """初始化用户控制器"""
        try:
            print("初始化用户控制器...")
            
            # 创建子控制器实例
            self.user_data_controller = UserDataController()
            self.user_transactions_controller = UserTransactionsController()
            self.user_jobs_controller = UserJobsController()
            self.export_controller = ExportController()
            self.dashboard_controller = DashboardController()
            self.fragments_controller = FragmentsController()
            
            # 创建蓝图
            self.blueprint = Blueprint('user', __name__, url_prefix='/api')
            self._setup_routes()
            
            print("用户控制器初始化完成")
        except Exception as e:
            print(f"初始化用户控制器出错: {e}")
            traceback.print_exc()
            raise

    def _setup_routes(self):
        """设置路由"""
        # 用户数据相关API路由
        self.blueprint.route('/table/user')(self.user_data_controller.get_user_data)
        self.blueprint.route('/db/connection-pool')(self.user_data_controller.get_connection_pool_info)
        self.blueprint.route('/date-range/default')(self.user_data_controller.get_default_date_range)
        
        # 用户交易相关API路由
        self.blueprint.route('/user/transactions')(self.user_transactions_controller.get_user_transactions)
        
        # 用户作业相关API路由
        self.blueprint.route('/user/jobs')(self.user_jobs_controller.get_user_jobs)
        
        # 控制台首页数据
        self.blueprint.route('/dashboard/stats')(self.dashboard_controller.get_dashboard_stats)
        
        # 导出相关路由
        self.blueprint.route('/export/user-summary')(self.export_controller.export_user_summary)
        self.blueprint.route('/export/user-transactions')(self.export_controller.export_user_transactions)
        self.blueprint.route('/export/user-jobs')(self.export_controller.export_user_jobs)
        
        # 界面片段路由
        self.blueprint.route('/user/detail-fragment')(self.fragments_controller.render_user_detail_fragment)
        self.blueprint.route('/user/jobs-fragment')(self.fragments_controller.render_user_jobs_fragment)

# 创建控制器实例
user_controller = UserController() 