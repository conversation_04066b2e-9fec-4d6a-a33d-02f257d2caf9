<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>用户作业详情</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="{{ url_for('static', filename='res/layui/css/layui.css') }}" media="all">
  <style>
    .detail-form-item {
      margin-bottom: 8px;
    }
    .detail-form-label {
      display: inline-block;
      width: 100px;
      text-align: right;
      padding-right: 10px;
      font-weight: bold;
    }
    .detail-form-value {
      display: inline-block;
      min-width: 150px;
    }
    .filter-form {
      margin-bottom: 15px;
      background-color: #f8f8f8;
      padding: 15px;
      border-radius: 4px;
    }
    .pagination {
      margin-top: 15px;
      text-align: center;
    }
    /* 悬浮高亮：修改鼠标hover行背景色 */
    .layui-table tbody tr:hover,
    .layui-table-hover,
    .layui-table[lay-skin="row"] tbody tr:hover {
      background-color: #f2f9ff !important; /* 浅蓝色，符合现代UI设计趋势 */
    }
  </style>
</head>
<body>
  <div class="layui-container">
    <!-- 用户作业详情 -->
    <div class="layui-card">
      <!-- <div class="layui-card-header">用户作业详情</div> -->
      <div class="layui-card-body">
        <div class="layui-row">
          <div class="layui-col-md6">
            <div class="detail-form-item">
              <span class="detail-form-label">用户名:</span>
              <span class="detail-form-value" id="detail-username"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">客户名称:</span>
              <span class="detail-form-value" id="detail-customer"></span>
            </div>
          </div>
          <div class="layui-col-md6">
            <div class="detail-form-item">
              <span class="detail-form-label">作业总数:</span>
              <span class="detail-form-value" id="detail-job-count"></span>
            </div>
            <div class="detail-form-item">
              <span class="detail-form-label">总运行时长:</span>
              <span class="detail-form-value" id="detail-total-duration"></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 过滤条件 -->
    <div class="layui-card">
      <div class="layui-card-header">作业记录过滤</div>
      <div class="layui-card-body">
        <form class="layui-form filter-form" id="jobs-filter-form">
          <div class="layui-form-item">
            <div class="layui-inline">
              <label class="layui-form-label">日期范围:</label>
              <div class="layui-input-inline" style="width: 320px;">
                <input type="text" name="date-range" id="jobs-date-range" placeholder=" - " autocomplete="off" class="layui-input">
              </div>
            </div>
            <div class="layui-inline">
              <button type="button" class="layui-btn" lay-submit lay-filter="jobs-filter-submit">应用过滤</button>
              <button type="button" class="layui-btn layui-btn-primary" id="reset-filter-btn">重置</button>
              <button type="button" class="layui-btn layui-btn-normal" id="export-jobs-btn">
                <i class="layui-icon layui-icon-export"></i> 导出数据
              </button>
            </div>
          </div>
        </form>
        
        <!-- 作业记录表格 -->
        <table id="jobs-table" lay-filter="jobs-table"></table>
        
        <!-- 分页区域 -->
        <div id="jobs-table-page" class="pagination"></div>
      </div>
    </div>
  </div>
</body>
</html> 