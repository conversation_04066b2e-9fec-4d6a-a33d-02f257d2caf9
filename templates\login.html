<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>市公共算力服务平台 - 登录</title>
  <link rel="icon" href="{{ url_for('static', filename='img/logo.png') }}">
  <!-- 引入Roboto字体（unpkg版本） -->
  <link href="https://unpkg.com/@fontsource/roboto@4.5.0/index.css" rel="stylesheet">
  <style>
    /* 变量定义 */
    :root {
      --body-color: #DBE0F9;
      --brand-primary: #6065D9;
      --brand-light-primary: #17D7FA;
    }
    
    /* 全局样式 */
    body {
      margin: 0;
      height: 100vh;
      overflow: hidden;
      background-color: var(--body-color);
      font-family: 'Roboto', sans-serif;
    }
    
    .svg-top {
      position: absolute;
      top: -900px;
      right: -300px;
    }
    
    .svg-bottom {
      position: absolute;
      bottom: -500px;
      left: -200px;
    }
    
    /* 容器样式 */
    .container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    
    .wrapper {
      padding: 40px;
      background-color: #fff;
      border-radius: 20px;
      width: 380px;
      z-index: 1;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    /* 头部样式 */
    header {
      margin-bottom: 30px;
      text-align: center;
    }
    
    .logo {
      width: 70px;
      height: 70px;
      margin: 0 auto 15px;
    }

    .logo img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    
    header h1 {
      color: var(--brand-primary);
      font-size: 28px;
      font-weight: 500;
      margin: 0;
    }
    
    header p {
      color: #888;
      font-size: 16px;
      font-weight: 300;
      margin: 5px 0 0 0;
    }
    
    /* 主要内容区样式 */
    .main-content form {
      display: flex;
      flex-direction: column;
    }
    
    .login-message {
        padding: 10px 15px;
        border-radius: 6px;
        font-size: 14px;
        margin-bottom: 10px;
        display: none; /* 默认隐藏 */
        text-align: center;
        transition: all 0.3s ease;
    }
    .login-message.error {
        background-color: #ffebee;
        color: #c62828;
    }
    .login-message.success {
        background-color: #e8f5e9;
        color: #2e7d32;
    }

    .main-content input {
      border: none;
      display: block;
      width: 100%;
      height: 50px;
      margin: 10px 0;
      font-size: 18px;
      color: #999;
      padding-left: 10px;
      box-sizing: border-box;
      border-radius: 8px;
      background-color: #f5f7ff;
      transition: all 0.3s ease;
    }
    
    .main-content input::placeholder {
      color: #999;
      font-size: 18px;
      font-weight: 300;
    }
    
    .main-content input:focus {
      outline: none;
      background-color: #e8ebff;
      box-shadow: 0 0 0 3px rgba(96, 101, 217, 0.2);
    }
    
    .line {
      width: 100%;
      height: 2px;
      background-color: #99999955;
      transition: all 0.3s ease;
    }
    
    .main-content button {
      background: linear-gradient(to right, var(--brand-primary), var(--brand-light-primary));
      border: none;
      border-radius: 50px;
      font-size: 18px;
      font-weight: 300;
      color: #fff;
      display: block;
      width: 100%;
      height: 48px;
      margin: 25px 0 0;
      outline: none;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(96, 101, 217, 0.3);
    }
    
    .main-content button:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(96, 101, 217, 0.4);
    }
    
    /* 页脚样式 */
    footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 40px;
      font-size: 14px;
    }
    
    footer p {
      margin: 0;
      font-weight: 300;
    }
    
    footer a {
      color: var(--brand-primary);
      text-decoration: none;
      transition: color 0.3s ease;
    }
    
    footer a:hover {
      color: #4a50c9;
      text-decoration: underline;
    }
    
    /* 响应式设计 */
    @media (min-width: 320px) and (max-width: 768px) {
      .wrapper {
        margin: 0 10px;
        padding: 30px;
        width: auto;
      }
      
      header h1 {
        font-size: 26px;
      }
      
      .main-content input {
        height: 45px;
        font-size: 16px;
      }
      
      .main-content input::placeholder {
        font-size: 16px;
      }
    }
  </style>
</head>
<body>
  <!-- 顶部装饰SVG -->
  <div class="svg-top">
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="1337" width="1337">
      <defs>
        <path id="path-1" opacity="1" fill-rule="evenodd" d="M1337,668.5 C1337,1037.455193874239 1037.455193874239,1337 668.5,1337 C523.6725684305388,1337 337,1236 370.50000000000006,1094 C434.03835568300906,824.6732385973953 6.906089672974592e-14,892.6277623047779 0,668.5000000000001 C0,299.5448061257611 299.5448061257609,1.1368683772161603e-13 668.4999999999999,0 C1037.455193874239,0 1337,299.544806125761 1337,668.5Z"/>
        <linearGradient id="linearGradient-2" x1="0.79" y1="0.62" x2="0.21" y2="0.86">
          <stop offset="0" stop-color="rgb(88,62,213)" stop-opacity="1"/>
          <stop offset="1" stop-color="rgb(23,215,250)" stop-opacity="1"/>
        </linearGradient>
      </defs>
      <g opacity="1">
        <use xlink:href="#path-1" fill="url(#linearGradient-2)" fill-opacity="1"/>
      </g>
    </svg>
  </div>
  
  <!-- 底部装饰SVG -->
  <div class="svg-bottom">
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="896" width="967.8852157128662">
      <defs>
        <path id="path-2" opacity="1" fill-rule="evenodd" d="M896,448 C1142.6325445712241,465.5747656464056 695.2579309733121,896 448,896 C200.74206902668806,896 5.684341886080802e-14,695.2579309733121 0,448.0000000000001 C0,200.74206902668806 200.74206902668791,5.684341886080802e-14 447.99999999999994,0 C695.2579309733121,0 475,418 896,448Z"/>
        <linearGradient id="linearGradient-3" x1="0.5" y1="0" x2="0.5" y2="1">
          <stop offset="0" stop-color="rgb(40,175,240)" stop-opacity="1"/>
          <stop offset="1" stop-color="rgb(18,15,196)" stop-opacity="1"/>
        </linearGradient>
      </defs>
      <g opacity="1">
        <use xlink:href="#path-2" fill="url(#linearGradient-3)" fill-opacity="1"/>
      </g>
    </svg>
  </div>
  
  <!-- 登录容器 -->
  <section class="container">
    <section class="wrapper">
      <header>
        <div class="logo">
          <img src="{{ url_for('static', filename='img/logo.png') }}" alt="Logo">
        </div>
        <h1>市公共算力服务平台</h1>
        <p>客户信息管理系统</p>
      </header>
      <section class="main-content">
        <form id="login-form">
          <div id="login-message" class="login-message"></div>
          <input type="text" name="username" placeholder="用户名" required>
          <div class="line"></div>
          <input type="password" name="password" placeholder="密码" required>
          <div class="line"></div>
          <button type="submit">登 录</button>
        </form>
      </section>
      <footer>
        <p><a href="javascript:;" title="忘记密码">忘记密码?</a></p>
        <p><a href="javascript:;" title="注册新用户">注册新用户</a></p>
      </footer>
    </section>
  </section>

  <!-- Layui用于消息提示 -->
  <script src="{{ url_for('static', filename='res/layui/layui.js') }}"></script>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 1. 输入框动效
      const inputs = document.querySelectorAll('.main-content input');
      inputs.forEach(input => {
        input.addEventListener('focus', function() {
          const line = this.nextElementSibling;
          if (line && line.classList.contains('line')) {
            line.style.backgroundColor = 'var(--brand-primary)';
          }
        });
        input.addEventListener('blur', function() {
          const line = this.nextElementSibling;
          if (line && line.classList.contains('line')) {
            line.style.backgroundColor = '#99999955';
          }
        });
      });

      // 2. Layui表单提交
      layui.use(['layer', 'jquery'], function(){
        var layer = layui.layer;
        var $ = layui.jquery;
        var $loginMessage = $('#login-message');

        $('#login-form').on('submit', function(e) {
            e.preventDefault();
            var username = $(this).find('input[name="username"]').val();
            var password = $(this).find('input[name="password"]').val();

            if (!username || !password) {
                $loginMessage.removeClass('success').addClass('error').text('用户名和密码不能为空').slideDown();
                return;
            } else {
                $loginMessage.slideUp();
            }

            $.ajax({
                url: '/api/auth/login',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    username: username,
                    password: password
                }),
                success: function(res) {
                    if (res.code === 0) {
                        $loginMessage.removeClass('error').addClass('success').text('登录成功').slideDown();
                        setTimeout(function(){
                            location.href = '/'; 
                        }, 1000);
                    } else {
                        $loginMessage.removeClass('success').addClass('error').text(res.msg).slideDown();
                    }
                },
                error: function() {
                    $loginMessage.removeClass('success').addClass('error').text('请求失败，请检查网络').slideDown();
                }
            });
        });
      });
    });
  </script>
</body>
</html> 