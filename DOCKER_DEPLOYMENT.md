# LayuiAdmin Flask Docker 部署指南

## 概述

本项目已配置为使用 Docker Compose 进行部署，适用于 EulerOS 2.0 服务器环境。

## 系统要求

- EulerOS 2.0 服务器
- Docker Engine 20.10+
- Docker Compose 2.0+ 或 docker-compose 1.29+
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

## 网络配置

### Docker 镜像源配置

如果服务器无法访问 Docker Hub，已配置国内镜像源：

- 阿里云镜像源：`registry.cn-hangzhou.aliyuncs.com`
- 网易镜像源：`hub-mirror.c.163.com`
- 百度镜像源：`mirror.baidubce.com`
- 中科大镜像源：`docker.mirrors.ustc.edu.cn`

### 自动配置镜像源

启动脚本会自动检测并提示配置 Docker 镜像源。

## 项目配置

### 端口配置

- **应用端口**: 8667 (从原来的 83 修改)
- **容器内端口**: 8667
- **主机端口**: 8667

### Python 版本

- **Python**: 3.12.5

### 数据库配置

- **DuckDB**: `../bills.duckdb` (挂载到容器外，便于备份)
- **SQLite**: `./users.db` (用户认证数据库)

## 快速开始

### 1. 准备环境

```bash
# 确保在项目根目录
cd /path/to/layuiAdmin_flask

# 给脚本执行权限
chmod +x docker-start.sh docker-stop.sh
```

### 2. 启动服务

```bash
# 使用启动脚本（推荐）
./docker-start.sh

# 或者手动启动
docker-compose up --build -d
```

### 3. 访问应用

打开浏览器访问: `http://服务器IP:8667`

## 详细操作

### 构建和启动

```bash
# 构建镜像并启动容器
docker-compose up --build -d

# 查看容器状态
docker-compose ps

# 查看日志
docker logs layuiadmin-flask-app
```

### 停止服务

```bash
# 使用停止脚本
./docker-stop.sh

# 或者手动停止
docker-compose down
```

### 重启服务

```bash
docker-compose restart
```

## 数据持久化

### DuckDB 数据库

- **位置**: `../bills.duckdb` (项目根目录外)
- **挂载**: 挂载到容器的 `/data/bills.duckdb`
- **备份**: 直接备份主机上的 `../bills.duckdb` 文件

### SQLite 用户数据库

- **位置**: `./users.db` (项目根目录内)
- **挂载**: 挂载到容器的 `/app/users.db`
- **备份**: 直接备份主机上的 `./users.db` 文件

### 日志文件

- **位置**: `./logs/` 目录
- **挂载**: 挂载到容器的 `/app/logs/`

### 导出文件

- **位置**: `./static/exports/` 和 `./static/user_summary/`
- **挂载**: 挂载到容器对应目录

## 配置文件

### docker-compose.yml

主要的 Docker Compose 配置文件，定义了服务、网络和卷挂载。

### Dockerfile

基于 Python 3.12.5-slim 的镜像配置。

### .env

环境变量配置文件。

### config.json

应用程序配置文件，包含数据库和应用设置。

## 故障排除

### 常见问题

1. **端口冲突**

   ```bash
   # 检查端口占用
   netstat -tlnp | grep 8667

   # 修改docker-compose.yml中的端口映射
   ports:
     - "其他端口:8667"
   ```

2. **数据库文件权限问题**

   ```bash
   # 确保数据库文件有正确权限
   chmod 666 ../bills.duckdb
   chmod 666 users.db
   ```

3. **容器启动失败**

   ```bash
   # 查看详细日志
   docker-compose logs layuiadmin-flask

   # 检查容器状态
   docker ps -a
   ```

4. **内存不足**

   ```bash
   # 检查系统内存
   free -h

   # 调整config.json中的memory_limit设置
   ```

### 日志查看

```bash
# 查看应用日志
docker logs layuiadmin-flask-app

# 实时查看日志
docker logs -f layuiadmin-flask-app

# 查看Docker Compose日志
docker-compose logs
```

## 备份策略

### 数据库备份

```bash
# 备份DuckDB
cp ../bills.duckdb ../bills.duckdb.backup.$(date +%Y%m%d_%H%M%S)

# 备份SQLite
cp users.db users.db.backup.$(date +%Y%m%d_%H%M%S)
```

### 完整备份脚本

```bash
#!/bin/bash
BACKUP_DIR="/backup/layuiadmin/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 备份数据库
cp ../bills.duckdb "$BACKUP_DIR/"
cp users.db "$BACKUP_DIR/"

# 备份配置
cp docker-compose.yml "$BACKUP_DIR/"
cp .env "$BACKUP_DIR/"
cp config.json "$BACKUP_DIR/"

# 备份日志
cp -r logs "$BACKUP_DIR/"

echo "备份完成: $BACKUP_DIR"
```

## 更新部署

```bash
# 停止服务
docker-compose down

# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up --build -d
```

## 安全建议

1. 修改 `.env` 文件中的 `SECRET_KEY`
2. 确保防火墙只开放必要端口
3. 定期备份数据库文件
4. 监控容器资源使用情况
5. 定期更新 Docker 镜像

## 监控

### 健康检查

容器配置了健康检查，每 30 秒检查一次应用状态。

### 资源监控

```bash
# 查看容器资源使用
docker stats layuiadmin-flask-app

# 查看系统资源
htop
```
