#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
卡时数据相关功能
"""

import traceback

class CardHoursMixin:
    """卡时数据相关功能"""
    
    def add_shared_card_hours_to_summary(self, summary_df, start_date="", end_date=""):
        """将共享卡时数据添加到用户汇总中
        共享卡时 = transaction_details表的[原始金额(元)]合计/6
        条件1 bill_information.用户名=transaction_details.用户名
        条件2 bill_information.产品 like '共享'
        条件3 bill_information.支付时间在过滤日期范围之间
        """
        try:
            print("添加共享卡时数据...")
            # 检查bill_information表是否存在
            if not self._check_table_exists("bill_information"):
                self.logger.warning("bill_information表不存在，无法获取共享卡时数据")
                summary_df['共享卡时'] = 0
                return summary_df
            
            # 构建日期条件
            date_condition = ""
            if start_date and end_date:
                date_condition = f'AND b."支付时间" >= \'{start_date} 00:00:00\' AND b."支付时间" <= \'{end_date} 23:59:59\''
            elif start_date:
                date_condition = f'AND b."支付时间" >= \'{start_date} 00:00:00\''
            elif end_date:
                date_condition = f'AND b."支付时间" <= \'{end_date} 23:59:59\''
            
            # 获取共享卡时数据
            shared_hours_sql = f"""
            WITH 共享数据 AS (
                SELECT 
                    b."用户名",
                    CAST(b."原始金额(元)" AS DOUBLE) AS 原始金额
                FROM bill_information b
                WHERE b."产品" LIKE '%共享%'
                {date_condition}
            )
            SELECT 
                s."用户名",
                SUM(s."原始金额") / 6 AS 共享卡时
            FROM 共享数据 s
            GROUP BY s."用户名"
            """
            
            # 执行查询
            print(f"执行SQL: {shared_hours_sql}")
            shared_hours_data = self.db_manager.execute_query(shared_hours_sql)
            
            if shared_hours_data is not None and not shared_hours_data.empty:
                print(f"共享卡时数据: {shared_hours_data.shape}")
                # 将共享卡时数据合并到用户汇总中
                summary_df = summary_df.merge(shared_hours_data, on='用户名', how='left')
                # 填充NaN值为0
                summary_df['共享卡时'] = summary_df['共享卡时'].fillna(0)
                
                # 格式化共享卡时，保留2位小数
                summary_df['共享卡时'] = summary_df['共享卡时'].round(2)
            else:
                print("没有获取到共享卡时数据")
                # 如果没有获取到共享卡时数据，添加一个全为0的列
                summary_df['共享卡时'] = 0
                
            return summary_df
            
        except Exception as e:
            self.logger.error(f"获取共享卡时数据时出错: {str(e)}")
            traceback.print_exc()
            # 出错时添加一个全为0的列
            summary_df['共享卡时'] = 0
            return summary_df
            
    def add_exclusive_card_hours_to_summary(self, summary_df, start_date="", end_date=""):
        """将专属卡时数据添加到用户汇总中
        专属卡时 = transaction_details表的[原始金额(元)]合计/5.208333333
        条件1 bill_information.用户名=transaction_details.用户名
        条件2 bill_information.产品 like '专属'
        条件3 bill_information.支付时间在过滤日期范围之间
        """
        try:
            print("添加专属卡时数据...")
            # 检查bill_information表是否存在
            if not self._check_table_exists("bill_information"):
                self.logger.warning("bill_information表不存在，无法获取专属卡时数据")
                summary_df['专属卡时'] = 0
                return summary_df
            
            # 构建日期条件
            date_condition = ""
            if start_date and end_date:
                date_condition = f'AND b."支付时间" >= \'{start_date} 00:00:00\' AND b."支付时间" <= \'{end_date} 23:59:59\''
            elif start_date:
                date_condition = f'AND b."支付时间" >= \'{start_date} 00:00:00\''
            elif end_date:
                date_condition = f'AND b."支付时间" <= \'{end_date} 23:59:59\''
            
            # 获取专属卡时数据
            exclusive_hours_sql = f"""
            WITH 专属数据 AS (
                SELECT 
                    b."用户名",
                    CAST(b."原始金额(元)" AS DOUBLE) AS 原始金额
                FROM bill_information b
                WHERE b."产品" LIKE '%专属%'
                {date_condition}
            )
            SELECT 
                e."用户名",
                SUM(e."原始金额") / 5.208333333 AS 专属卡时
            FROM 专属数据 e
            GROUP BY e."用户名"
            """
            
            # 执行查询
            print(f"执行SQL: {exclusive_hours_sql}")
            exclusive_hours_data = self.db_manager.execute_query(exclusive_hours_sql)
            
            if exclusive_hours_data is not None and not exclusive_hours_data.empty:
                print(f"专属卡时数据: {exclusive_hours_data.shape}")
                # 将专属卡时数据合并到用户汇总中
                summary_df = summary_df.merge(exclusive_hours_data, on='用户名', how='left')
                # 填充NaN值为0
                summary_df['专属卡时'] = summary_df['专属卡时'].fillna(0)
                
                # 格式化专属卡时，保留2位小数
                summary_df['专属卡时'] = summary_df['专属卡时'].round(2)
            else:
                print("没有获取到专属卡时数据")
                # 如果没有获取到专属卡时数据，添加一个全为0的列
                summary_df['专属卡时'] = 0
                
            return summary_df
            
        except Exception as e:
            self.logger.error(f"获取专属卡时数据时出错: {str(e)}")
            traceback.print_exc()
            # 出错时添加一个全为0的列
            summary_df['专属卡时'] = 0
            return summary_df
            
    def add_exclusive_card_hours_adjusted_to_summary(self, summary_df, start_date="", end_date=""):
        """将专属卡时(除误差)数据添加到用户汇总中
        专属卡时(除误差) = (原始金额(元) - 特殊收入金额) / 5.208333333
        条件1 bill_information.用户名=transaction_details.用户名
        条件2 bill_information.产品 like '专属'
        条件3 bill_information.支付时间在过滤日期范围之间
        特殊收入金额指：transaction_details表中收入类型="收入"且备注包含"退订"或"延期"或"补"的金额
        """
        try:
            # 检查bill_information表和transaction_details表是否存在
            if not self._check_table_exists("bill_information") or not self._check_table_exists("transaction_details"):
                self.logger.warning("bill_information表或transaction_details表不存在，无法获取专属卡时(除误差)数据")
                summary_df['专属卡时(除误差)'] = 0
                return summary_df
            
            # 构建日期条件
            date_condition = ""
            if start_date and end_date:
                date_condition = f'AND b."支付时间" >= \'{start_date} 00:00:00\' AND b."支付时间" <= \'{end_date} 23:59:59\''
            elif start_date:
                date_condition = f'AND b."支付时间" >= \'{start_date} 00:00:00\''
            elif end_date:
                date_condition = f'AND b."支付时间" <= \'{end_date} 23:59:59\''
            
            # 获取专属卡时(除误差)数据
            exclusive_hours_adjusted_sql = f"""
            WITH 专属数据 AS (
                SELECT 
                    b."用户名",
                    CAST(b."原始金额(元)" AS DOUBLE) AS 原始金额
                FROM bill_information b
                WHERE b."产品" LIKE '%专属%'
                {date_condition}
            ),
            特殊收入 AS (
                SELECT 
                    t."用户名",
                    SUM(CAST(t."金额(元)" AS DOUBLE)) AS 特殊金额
                FROM transaction_details t
                WHERE t."收支类型" = '收入' 
                AND (t."备注" LIKE '%退订%' OR t."备注" LIKE '%延期%' OR t."备注" LIKE '%补%')
                {date_condition.replace('b."支付时间"', 't."交易时间"')}
                GROUP BY t."用户名"
            )
            SELECT 
                e."用户名",
                (SUM(e."原始金额") - COALESCE(s."特殊金额", 0)) / 5.208333333 AS 专属卡时调整
            FROM 专属数据 e
            LEFT JOIN 特殊收入 s ON e."用户名" = s."用户名"
            GROUP BY e."用户名", s."特殊金额"
            """
            
            # 执行查询
            exclusive_hours_adjusted_data = self.db_manager.execute_query(exclusive_hours_adjusted_sql)
            
            if exclusive_hours_adjusted_data is not None and not exclusive_hours_adjusted_data.empty:
                # 将专属卡时(除误差)数据合并到用户汇总中
                summary_df = summary_df.merge(exclusive_hours_adjusted_data.rename(
                    columns={'专属卡时调整': '专属卡时(除误差)'}), on='用户名', how='left')
                # 填充NaN值为0
                summary_df['专属卡时(除误差)'] = summary_df['专属卡时(除误差)'].fillna(0)
                
                # 格式化专属卡时(除误差)，保留2位小数
                summary_df['专属卡时(除误差)'] = summary_df['专属卡时(除误差)'].round(2)
            else:
                # 如果没有获取到专属卡时(除误差)数据，添加一个全为0的列
                summary_df['专属卡时(除误差)'] = 0
                
            return summary_df
            
        except Exception as e:
            self.logger.error(f"获取专属卡时(除误差)数据时出错: {str(e)}")
            traceback.print_exc()
            # 出错时添加一个全为0的列
            summary_df['专属卡时(除误差)'] = 0
            return summary_df 