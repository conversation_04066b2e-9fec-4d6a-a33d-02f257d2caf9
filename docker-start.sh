#!/bin/bash

# LayuiAdmin Flask Docker 启动脚本
# 适用于EulerOS 2.0服务器

echo "=== LayuiAdmin Flask Docker 部署脚本 ==="
echo "Python版本: 3.12.5"
echo "目标端口: 8667"
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "错误: Docker Compose 未安装，请先安装Docker Compose"
    exit 1
fi

# 配置Docker镜像源（如果需要）
DOCKER_DAEMON_CONFIG="/etc/docker/daemon.json"
if [ ! -f "$DOCKER_DAEMON_CONFIG" ]; then
    echo "检测到Docker未配置国内镜像源，建议配置以加速镜像下载"
    echo "是否要配置Docker国内镜像源？(y/n)"
    read -r configure_mirror
    if [ "$configure_mirror" = "y" ] || [ "$configure_mirror" = "Y" ]; then
        echo "配置Docker镜像源..."
        sudo mkdir -p /etc/docker
        sudo cp docker-daemon.json /etc/docker/daemon.json
        echo "重启Docker服务..."
        sudo systemctl restart docker
        echo "Docker镜像源配置完成"
    fi
fi

# 检查DuckDB文件是否存在
DUCKDB_FILE="../bills.duckdb"
if [ ! -f "$DUCKDB_FILE" ]; then
    echo "警告: DuckDB文件 $DUCKDB_FILE 不存在"
    echo "如果这是首次运行，DuckDB会自动创建数据库文件"
    echo "请确保父目录有写入权限"
    
    # 创建空的DuckDB文件
    touch "$DUCKDB_FILE"
    echo "已创建空的DuckDB文件: $DUCKDB_FILE"
fi

# 检查用户数据库文件
if [ ! -f "users.db" ]; then
    echo "警告: 用户数据库文件 users.db 不存在，应用启动时会自动创建"
fi

# 创建必要的目录
mkdir -p logs
mkdir -p static/exports
mkdir -p static/user_summary

echo "开始构建和启动Docker容器..."

# 停止现有容器（如果存在）
echo "停止现有容器..."
docker-compose down 2>/dev/null || docker compose down 2>/dev/null

# 构建并启动容器
echo "构建并启动新容器..."
if command -v docker-compose &> /dev/null; then
    docker-compose up --build -d
else
    docker compose up --build -d
fi

# 检查容器状态
echo ""
echo "检查容器状态..."
sleep 5

if command -v docker-compose &> /dev/null; then
    docker-compose ps
else
    docker compose ps
fi

echo ""
echo "=== 部署完成 ==="
echo "应用访问地址: http://localhost:8667"
echo "或者: http://服务器IP:8667"
echo ""
echo "查看日志: docker logs layuiadmin-flask-app"
echo "停止服务: docker-compose down 或 docker compose down"
echo "重启服务: docker-compose restart 或 docker compose restart"
echo ""
echo "数据库文件位置:"
echo "  - DuckDB: ../bills.duckdb (已挂载)"
echo "  - SQLite: ./users.db (已挂载)"
echo ""
