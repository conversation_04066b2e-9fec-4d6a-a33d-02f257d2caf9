#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易记录相关功能
"""

import traceback
from .base import DBConnection

class TransactionsMixin:
    """交易记录相关功能"""
    
    def get_user_transactions(self, username, filters, page=1, page_size=15):
        """获取用户交易记录（分页）"""
        try:
            print(f"开始获取用户 '{username}' 的交易记录，过滤条件: {filters}")
            # 确保所有条件都使用表别名
            modified_filters = []
            for filter_condition in filters:
                if not filter_condition.strip().startswith('t.') and filter_condition.find('.') == -1:
                    parts = filter_condition.split(' ', 1)
                    if len(parts) > 1:
                        modified_filter = f't."{parts[0]}" {parts[1]}'
                        modified_filters.append(modified_filter)
                    else:
                        modified_filters.append(filter_condition)
                else:
                    modified_filters.append(filter_condition)
            
            print(f"修改后的过滤条件: {modified_filters}")
            
            # 构建查询
            where_clause = " AND ".join(modified_filters) if modified_filters else "1=1"
            
            # 计算总记录数
            count_sql = f"""
            SELECT COUNT(*) AS total_count 
            FROM transaction_details t
            WHERE {where_clause}
            """
            
            print(f"执行计数SQL: {count_sql}")
            
            # 获取分页数据 - 移除不存在的列"交易描述"
            page_sql = f"""
            SELECT 
                t."交易编号", 
                t."账户ID", 
                t."用户名", 
                t."客户名称", 
                t."收支类型", 
                t."金额(元)" as 金额,
                t."交易时间", 
                t."备注", 
                t."账期",
                t."交易类型",
                t."交易渠道",
                t."现金余额(元)", 
                t."信用额度(元)"
            FROM transaction_details t
            WHERE {where_clause}
            ORDER BY t."交易时间" DESC, t."交易编号" DESC
            LIMIT {page_size} OFFSET {(page - 1) * page_size}
            """
            
            print(f"执行查询SQL: {page_sql}")
            
            # 执行查询
            with DBConnection(self.db_manager) as conn:
                # 先检查表结构
                columns = conn.execute("PRAGMA table_info(transaction_details)").fetchall()
                column_names = [col[1] for col in columns]
                print(f"transaction_details表的列名: {column_names}")
                
                # 获取总记录数
                count_result = conn.execute(count_sql).fetchone()
                total_count = count_result[0] if count_result else 0
                print(f"符合条件的总记录数: {total_count}")
                
                # 获取分页数据
                df = None
                if total_count > 0:
                    try:
                        df = conn.execute(page_sql).fetchdf()
                        print(f"查询结果: {df.shape if df is not None else 'None'}")
                        if df is not None and not df.empty:
                            print(f"查询结果列: {df.columns.tolist()}")
                    except Exception as e:
                        print(f"执行分页查询时出错: {str(e)}")
                        import traceback
                        traceback.print_exc()
                
                # 计算总页数
                total_pages = max(1, (total_count + page_size - 1) // page_size)
                
                return {
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'page': page,
                    'page_size': page_size,
                    'data': df
                }
                
        except Exception as e:
            self.logger.error(f"获取用户交易记录时出错: {str(e)}")
            traceback.print_exc()
            return None 