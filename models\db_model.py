#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库模型
实现数据库相关操作
"""

import os
import logging
import traceback
import json

class DBModel:
    def __init__(self, db_manager):
        """初始化数据库模型"""
        self.db_manager = db_manager
        # 设置日志
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("db_model")
        if not logger.handlers:
            # 确保logs目录存在
            logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
                
            handler = logging.FileHandler(os.path.join(logs_dir, "db_model.log"))
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
        
    def get_database_file_size(self):
        """获取数据库文件大小"""
        try:
            # 获取数据库文件路径
            db_path = self.db_manager.db_path
            
            # 检查文件是否存在
            if not os.path.exists(db_path):
                self.logger.warning(f"数据库文件不存在: {db_path}")
                return "0 B"
                
            # 获取文件大小（字节）
            size_bytes = os.path.getsize(db_path)
            
            # 转换为合适的单位
            units = ["B", "KB", "MB", "GB", "TB"]
            unit_index = 0
            size = float(size_bytes)
            
            while size >= 1024 and unit_index < len(units) - 1:
                size /= 1024
                unit_index += 1
                
            return f"{size:.2f} {units[unit_index]}"
        except Exception as e:
            self.logger.error(f"获取数据库文件大小失败: {str(e)}")
            self.logger.error(traceback.format_exc())
            return "未知" 