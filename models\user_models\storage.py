#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OBS存储相关功能
"""

import traceback

class OBSStorageMixin:
    """OBS存储相关功能"""
    
    def add_obs_storage_to_summary(self, summary_df, date_filter=""):
        """将OBS容量数据添加到用户汇总中"""
        try:
            print("添加OBS容量数据...")
            # 检查bill_information表是否存在
            if not self._check_table_exists("bill_information"):
                        self.logger.warning("bill_information表不存在，无法获取OBS容量数据")
                        summary_df['OBS容量'] = "0.00GB"
                        return summary_df
            
            # 构建查询 - 使用与原始项目相同的SQL
            obs_sql = f"""
            WITH 目标账期 AS (
                SELECT 
                    MAX(b."账期") AS 目标账期 
                FROM bill_information b
                WHERE b."产品" LIKE '对象存储 OBS%'
                {('AND b."账期" <= ' + "'" + date_filter + "'") if date_filter else ""}
            ),
            用户OBS数据 AS (
                SELECT 
                    b."用户名",
                    b."名称/ID" AS 存储桶,
                    b."资源用量",
                    ROW_NUMBER() OVER (PARTITION BY b."用户名", b."名称/ID" ORDER BY b."支付时间" DESC, b."账单号" DESC) AS rn
                FROM bill_information b
                JOIN 目标账期 t ON b."账期" = t.目标账期
                WHERE b."产品" LIKE '对象存储 OBS%'
            )
            SELECT 
                o."用户名",
                SUM(CASE 
                    WHEN o."资源用量" LIKE '%GB%' THEN CAST(REPLACE(o."资源用量", 'GB', '') AS DOUBLE)
                    WHEN o."资源用量" LIKE '%TB%' THEN CAST(REPLACE(o."资源用量", 'TB', '') AS DOUBLE) * 1024
                    ELSE 0.0 
                END) AS OBS容量GB
            FROM 用户OBS数据 o
            WHERE o.rn = 1
            GROUP BY o."用户名"
            """
            
            # 执行查询
            print(f"执行SQL: {obs_sql}")
            obs_data = self.db_manager.execute_query(obs_sql)
            
            if obs_data is not None and not obs_data.empty:
                print(f"OBS数据: {obs_data.shape}")
                # 将OBS容量数据合并到用户汇总中
                summary_df = summary_df.merge(obs_data, on='用户名', how='left')
                # 填充NaN值为0
                summary_df['OBS容量GB'] = summary_df['OBS容量GB'].fillna(0)
                
                # 添加格式化的OBS容量列(GB/TB自动转换)
                def format_storage_size(size_gb):
                    if size_gb >= 1024:
                        return f"{size_gb/1024:.2f}TB"
                    else:
                        return f"{size_gb:.2f}GB"
                
                # 保存原始数值，用于排序和过滤
                summary_df['OBS容量数值'] = summary_df['OBS容量GB']
                # 添加格式化的显示值
                summary_df['OBS容量'] = summary_df['OBS容量GB'].apply(format_storage_size)
                # 删除原始的GB列
                summary_df = summary_df.drop(columns=['OBS容量GB'])
            else:
                print("没有获取到OBS数据")
                # 如果没有获取到OBS数据，添加一个全为0的列
                summary_df['OBS容量数值'] = 0
                summary_df['OBS容量'] = "0.00GB"
                
            return summary_df
            
        except Exception as e:
            self.logger.error(f"获取OBS容量数据时出错: {str(e)}")
            traceback.print_exc()
            # 出错时添加一个全为0的列
            summary_df['OBS容量数值'] = 0
            summary_df['OBS容量'] = "0.00GB"
            return summary_df 