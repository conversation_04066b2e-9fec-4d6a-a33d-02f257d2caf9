# Git相关
.git
.gitignore
.gitattributes

# Docker相关
Dockerfile
docker-compose.yml
.dockerignore

# 文档
README.md
DOCKER_DEPLOYMENT.md
*.md

# 脚本
docker-start.sh
docker-stop.sh
start_app.bat
stop_app.sh

# 环境文件
.env

# Python缓存
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp
.tmp/

# 备份文件
*.backup
*.bak
